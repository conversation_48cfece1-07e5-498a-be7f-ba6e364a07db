{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\dev\\\\saas_jobseeker\\\\client\\\\src\\\\components\\\\SearchFilters.js\";\nimport React from 'react';\nimport { agencies, locations } from '../data/mockJobs';\nimport './SearchFilters.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SearchFilters = ({\n  searchTerm,\n  onSearchChange,\n  selectedAgency,\n  onAgencyChange,\n  selectedLocation,\n  onLocationChange\n}) => {\n  const handleSearchSubmit = e => {\n    e.preventDefault();\n    // Search is handled by onChange, but we prevent form submission\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"search-filters\",\n    role: \"search\",\n    \"aria-label\": \"Job search and filters\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSearchSubmit,\n      className: \"search-form\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group search-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"job-search\",\n            className: \"form-label\",\n            children: \"Search Jobs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"job-search\",\n            type: \"text\",\n            className: \"form-input search-input\",\n            placeholder: \"Search by job title or agency...\",\n            value: searchTerm,\n            onChange: e => onSearchChange(e.target.value),\n            \"aria-describedby\": \"search-help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"search-help\",\n            className: \"sr-only\",\n            children: \"Enter keywords to search job titles and agency names\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"agency-filter\",\n            className: \"form-label\",\n            children: \"Agency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"agency-filter\",\n            className: \"form-select\",\n            value: selectedAgency,\n            onChange: e => onAgencyChange(e.target.value),\n            \"aria-describedby\": \"agency-help\",\n            children: agencies.map(agency => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: agency,\n              children: agency\n            }, agency, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"agency-help\",\n            className: \"sr-only\",\n            children: \"Filter jobs by government agency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"location-filter\",\n            className: \"form-label\",\n            children: \"Location\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"location-filter\",\n            className: \"form-select\",\n            value: selectedLocation,\n            onChange: e => onLocationChange(e.target.value),\n            \"aria-describedby\": \"location-help\",\n            children: locations.map(location => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: location,\n              children: location\n            }, location, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"location-help\",\n            className: \"sr-only\",\n            children: \"Filter jobs by location\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = SearchFilters;\nexport default SearchFilters;\nvar _c;\n$RefreshReg$(_c, \"SearchFilters\");", "map": {"version": 3, "names": ["React", "agencies", "locations", "jsxDEV", "_jsxDEV", "SearchFilters", "searchTerm", "onSearchChange", "selectedAgency", "onAgencyChange", "selectedLocation", "onLocationChange", "handleSearchSubmit", "e", "preventDefault", "className", "role", "children", "onSubmit", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "type", "placeholder", "value", "onChange", "target", "map", "agency", "location", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/dev/saas_jobseeker/client/src/components/SearchFilters.js"], "sourcesContent": ["import React from 'react';\nimport { agencies, locations } from '../data/mockJobs';\nimport './SearchFilters.css';\n\nconst SearchFilters = ({\n  searchTerm,\n  onSearchChange,\n  selectedAgency,\n  onAgencyChange,\n  selectedLocation,\n  onLocationChange\n}) => {\n  const handleSearchSubmit = (e) => {\n    e.preventDefault();\n    // Search is handled by onChange, but we prevent form submission\n  };\n\n  return (\n    <div className=\"search-filters\" role=\"search\" aria-label=\"Job search and filters\">\n      <form onSubmit={handleSearchSubmit} className=\"search-form\">\n        <div className=\"search-row\">\n          <div className=\"form-group search-group\">\n            <label htmlFor=\"job-search\" className=\"form-label\">\n              Search Jobs\n            </label>\n            <input\n              id=\"job-search\"\n              type=\"text\"\n              className=\"form-input search-input\"\n              placeholder=\"Search by job title or agency...\"\n              value={searchTerm}\n              onChange={(e) => onSearchChange(e.target.value)}\n              aria-describedby=\"search-help\"\n            />\n            <div id=\"search-help\" className=\"sr-only\">\n              Enter keywords to search job titles and agency names\n            </div>\n          </div>\n          \n          <div className=\"form-group filter-group\">\n            <label htmlFor=\"agency-filter\" className=\"form-label\">\n              Agency\n            </label>\n            <select\n              id=\"agency-filter\"\n              className=\"form-select\"\n              value={selectedAgency}\n              onChange={(e) => onAgencyChange(e.target.value)}\n              aria-describedby=\"agency-help\"\n            >\n              {agencies.map(agency => (\n                <option key={agency} value={agency}>\n                  {agency}\n                </option>\n              ))}\n            </select>\n            <div id=\"agency-help\" className=\"sr-only\">\n              Filter jobs by government agency\n            </div>\n          </div>\n          \n          <div className=\"form-group filter-group\">\n            <label htmlFor=\"location-filter\" className=\"form-label\">\n              Location\n            </label>\n            <select\n              id=\"location-filter\"\n              className=\"form-select\"\n              value={selectedLocation}\n              onChange={(e) => onLocationChange(e.target.value)}\n              aria-describedby=\"location-help\"\n            >\n              {locations.map(location => (\n                <option key={location} value={location}>\n                  {location}\n                </option>\n              ))}\n            </select>\n            <div id=\"location-help\" className=\"sr-only\">\n              Filter jobs by location\n            </div>\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default SearchFilters;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,SAAS,QAAQ,kBAAkB;AACtD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EACrBC,UAAU;EACVC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EACJ,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC;EAED,oBACEV,OAAA;IAAKW,SAAS,EAAC,gBAAgB;IAACC,IAAI,EAAC,QAAQ;IAAC,cAAW,wBAAwB;IAAAC,QAAA,eAC/Eb,OAAA;MAAMc,QAAQ,EAAEN,kBAAmB;MAACG,SAAS,EAAC,aAAa;MAAAE,QAAA,eACzDb,OAAA;QAAKW,SAAS,EAAC,YAAY;QAAAE,QAAA,gBACzBb,OAAA;UAAKW,SAAS,EAAC,yBAAyB;UAAAE,QAAA,gBACtCb,OAAA;YAAOe,OAAO,EAAC,YAAY;YAACJ,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAEnD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnB,OAAA;YACEoB,EAAE,EAAC,YAAY;YACfC,IAAI,EAAC,MAAM;YACXV,SAAS,EAAC,yBAAyB;YACnCW,WAAW,EAAC,kCAAkC;YAC9CC,KAAK,EAAErB,UAAW;YAClBsB,QAAQ,EAAGf,CAAC,IAAKN,cAAc,CAACM,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;YAChD,oBAAiB;UAAa;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACFnB,OAAA;YAAKoB,EAAE,EAAC,aAAa;YAACT,SAAS,EAAC,SAAS;YAAAE,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAKW,SAAS,EAAC,yBAAyB;UAAAE,QAAA,gBACtCb,OAAA;YAAOe,OAAO,EAAC,eAAe;YAACJ,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAEtD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnB,OAAA;YACEoB,EAAE,EAAC,eAAe;YAClBT,SAAS,EAAC,aAAa;YACvBY,KAAK,EAAEnB,cAAe;YACtBoB,QAAQ,EAAGf,CAAC,IAAKJ,cAAc,CAACI,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;YAChD,oBAAiB,aAAa;YAAAV,QAAA,EAE7BhB,QAAQ,CAAC6B,GAAG,CAACC,MAAM,iBAClB3B,OAAA;cAAqBuB,KAAK,EAAEI,MAAO;cAAAd,QAAA,EAChCc;YAAM,GADIA,MAAM;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACTnB,OAAA;YAAKoB,EAAE,EAAC,aAAa;YAACT,SAAS,EAAC,SAAS;YAAAE,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAKW,SAAS,EAAC,yBAAyB;UAAAE,QAAA,gBACtCb,OAAA;YAAOe,OAAO,EAAC,iBAAiB;YAACJ,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAExD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnB,OAAA;YACEoB,EAAE,EAAC,iBAAiB;YACpBT,SAAS,EAAC,aAAa;YACvBY,KAAK,EAAEjB,gBAAiB;YACxBkB,QAAQ,EAAGf,CAAC,IAAKF,gBAAgB,CAACE,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;YAClD,oBAAiB,eAAe;YAAAV,QAAA,EAE/Bf,SAAS,CAAC4B,GAAG,CAACE,QAAQ,iBACrB5B,OAAA;cAAuBuB,KAAK,EAAEK,QAAS;cAAAf,QAAA,EACpCe;YAAQ,GADEA,QAAQ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACTnB,OAAA;YAAKoB,EAAE,EAAC,eAAe;YAACT,SAAS,EAAC,SAAS;YAAAE,QAAA,EAAC;UAE5C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACU,EAAA,GAlFI5B,aAAa;AAoFnB,eAAeA,aAAa;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}