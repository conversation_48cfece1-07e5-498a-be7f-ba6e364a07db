export interface Job {
  id: number;
  title: string;
  agency: string;
  location: string;
  closingDate: string;
  salary: string;
  type: string;
  classification: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  contactEmail: string;
  applicationInstructions: string;
  postedDate: string;
  workArrangements: string;
  securityClearance?: string;
  duration?: string;
}

export interface JobSummary {
  id: number;
  title: string;
  agency: string;
  location: string;
  closingDate: string;
  salary: string;
  type: string;
  classification: string;
}

export interface JobFilters {
  search?: string | undefined;
  agency?: string | undefined;
  location?: string | undefined;
  type?: string | undefined;
  classification?: string | undefined;
}

export interface PaginationParams {
  limit: number;
  offset: number;
}

export interface PaginationInfo {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

export interface JobsResponse {
  jobs: JobSummary[];
  pagination: PaginationInfo;
  filters: JobFilters;
}

export interface JobDetailsResponse {
  job: Job;
  relatedJobs: Array<{
    id: number;
    title: string;
    location: string;
    closingDate: string;
  }>;
}

export interface Application {
  id: number;
  jobId: number;
  jobTitle: string;
  agency: string;
  applicant: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string | null;
  };
  coverLetter: string | null;
  resumeFileName: string;
  submittedAt: string;
  status: 'submitted' | 'under_review' | 'shortlisted' | 'rejected' | 'hired';
  applicationNumber: string;
}

export interface ApplicationRequest {
  jobId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  coverLetter?: string;
  resumeFileName: string;
}

export interface ApplicationResponse {
  success: boolean;
  message: string;
  application: {
    applicationNumber: string;
    jobTitle: string;
    agency: string;
    submittedAt: string;
    status: string;
    nextSteps: string[];
    contactEmail: string;
  };
}

export interface ValidationError {
  error: string;
  message: string;
  errors: string[];
}

export interface ApiError {
  error: string;
  message: string;
  stack?: string;
}

export interface HealthCheckResponse {
  status: string;
  timestamp: string;
  uptime: number;
  environment: string;
}
