import { useState } from 'react';
import Header from './components/Header';
import Hero from './components/Hero';
import JobsSection from './components/JobsSection';
import ApplicationModal from './components/ApplicationModal';
import { mockJobs } from './data/mockJobs';
import { Job } from './types';
import './App.css';

function App(): JSX.Element {
  const [isHighContrast, setIsHighContrast] = useState<boolean>(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [filteredJobs, setFilteredJobs] = useState<Job[]>(mockJobs);

  const toggleHighContrast = (): void => {
    setIsHighContrast(!isHighContrast);
    document.documentElement.setAttribute(
      'data-theme',
      !isHighContrast ? 'high-contrast' : 'default'
    );
  };

  const handleApply = (job: Job): void => {
    setSelectedJob(job);
  };

  const closeModal = (): void => {
    setSelectedJob(null);
  };

  return (
    <div className="App">
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>
      
      <Header 
        isHighContrast={isHighContrast}
        onToggleContrast={toggleHighContrast}
      />
      
      <main id="main-content">
        <Hero />
        <JobsSection 
          jobs={filteredJobs}
          onJobsFilter={setFilteredJobs}
          onApply={handleApply}
        />
      </main>
      
      {selectedJob && (
        <ApplicationModal 
          job={selectedJob}
          onClose={closeModal}
        />
      )}
    </div>
  );
}

export default App;