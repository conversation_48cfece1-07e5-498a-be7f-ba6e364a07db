{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\dev\\\\saas_jobseeker\\\\client\\\\src\\\\components\\\\ApplicationModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ApplicationModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ApplicationModal = ({\n  job,\n  onClose\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    coverLetter: '',\n    resume: null\n  });\n  const modalRef = useRef(null);\n  const firstInputRef = useRef(null);\n  const previousFocusRef = useRef(null);\n  useEffect(() => {\n    // Store the previously focused element\n    previousFocusRef.current = document.activeElement;\n\n    // Focus the first input when modal opens\n    if (firstInputRef.current) {\n      firstInputRef.current.focus();\n    }\n\n    // Prevent body scroll\n    document.body.style.overflow = 'hidden';\n\n    // Cleanup function\n    return () => {\n      document.body.style.overflow = 'unset';\n      // Return focus to the previously focused element\n      if (previousFocusRef.current) {\n        previousFocusRef.current.focus();\n      }\n    };\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      files\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: files ? files[0] : value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    // Simulate form submission\n    alert('Application submitted successfully! (This is a demo)');\n    onClose();\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Escape') {\n      onClose();\n    }\n\n    // Trap focus within modal\n    if (e.key === 'Tab') {\n      const focusableElements = modalRef.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n      const firstElement = focusableElements[0];\n      const lastElement = focusableElements[focusableElements.length - 1];\n      if (e.shiftKey && document.activeElement === firstElement) {\n        e.preventDefault();\n        lastElement.focus();\n      } else if (!e.shiftKey && document.activeElement === lastElement) {\n        e.preventDefault();\n        firstElement.focus();\n      }\n    }\n  };\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-backdrop\",\n    onClick: handleBackdropClick,\n    onKeyDown: handleKeyDown,\n    role: \"dialog\",\n    \"aria-modal\": \"true\",\n    \"aria-labelledby\": \"modal-title\",\n    \"aria-describedby\": \"modal-description\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      ref: modalRef,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          id: \"modal-title\",\n          className: \"modal-title\",\n          children: [\"Apply for \", job.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: onClose,\n          \"aria-label\": \"Close application form\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            \"aria-hidden\": \"true\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"modal-description\",\n          className: \"job-summary\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Agency:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 16\n            }, this), \" \", job.agency]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Location:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 16\n            }, this), \" \", job.location]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Salary:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 16\n            }, this), \" \", job.salary]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"application-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"firstName\",\n                className: \"form-label\",\n                children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"required\",\n                  \"aria-label\": \"required\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                ref: firstInputRef,\n                id: \"firstName\",\n                name: \"firstName\",\n                type: \"text\",\n                className: \"form-input\",\n                value: formData.firstName,\n                onChange: handleInputChange,\n                required: true,\n                \"aria-required\": \"true\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"lastName\",\n                className: \"form-label\",\n                children: [\"Last Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"required\",\n                  \"aria-label\": \"required\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"lastName\",\n                name: \"lastName\",\n                type: \"text\",\n                className: \"form-input\",\n                value: formData.lastName,\n                onChange: handleInputChange,\n                required: true,\n                \"aria-required\": \"true\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"form-label\",\n              children: [\"Email Address \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                \"aria-label\": \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 31\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"email\",\n              name: \"email\",\n              type: \"email\",\n              className: \"form-input\",\n              value: formData.email,\n              onChange: handleInputChange,\n              required: true,\n              \"aria-required\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phone\",\n              className: \"form-label\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"phone\",\n              name: \"phone\",\n              type: \"tel\",\n              className: \"form-input\",\n              value: formData.phone,\n              onChange: handleInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"resume\",\n              className: \"form-label\",\n              children: [\"Resume \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                \"aria-label\": \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 24\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"resume\",\n              name: \"resume\",\n              type: \"file\",\n              className: \"form-input\",\n              onChange: handleInputChange,\n              accept: \".pdf,.doc,.docx\",\n              required: true,\n              \"aria-required\": \"true\",\n              \"aria-describedby\": \"resume-help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"resume-help\",\n              className: \"form-help\",\n              children: \"Accepted formats: PDF, DOC, DOCX (Max 5MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"coverLetter\",\n              className: \"form-label\",\n              children: \"Cover Letter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"coverLetter\",\n              name: \"coverLetter\",\n              className: \"form-input\",\n              rows: \"6\",\n              value: formData.coverLetter,\n              onChange: handleInputChange,\n              placeholder: \"Tell us why you're interested in this position...\",\n              \"aria-describedby\": \"cover-letter-help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"cover-letter-help\",\n              className: \"form-help\",\n              children: \"Optional: Explain your interest and qualifications for this role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: onClose,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              children: \"Submit Application\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(ApplicationModal, \"ytrNXyfKB40KKHVHmDXc66l39zA=\");\n_c = ApplicationModal;\nexport default ApplicationModal;\nvar _c;\n$RefreshReg$(_c, \"ApplicationModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ApplicationModal", "job", "onClose", "_s", "formData", "setFormData", "firstName", "lastName", "email", "phone", "coverLetter", "resume", "modalRef", "firstInputRef", "previousFocusRef", "current", "document", "activeElement", "focus", "body", "style", "overflow", "handleInputChange", "e", "name", "value", "files", "target", "prev", "handleSubmit", "preventDefault", "alert", "handleKeyDown", "key", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "length", "shift<PERSON>ey", "handleBackdropClick", "currentTarget", "className", "onClick", "onKeyDown", "role", "children", "ref", "id", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "agency", "location", "salary", "onSubmit", "htmlFor", "type", "onChange", "required", "accept", "rows", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/dev/saas_jobseeker/client/src/components/ApplicationModal.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ApplicationModal.css';\n\nconst ApplicationModal = ({ job, onClose }) => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    coverLetter: '',\n    resume: null\n  });\n  \n  const modalRef = useRef(null);\n  const firstInputRef = useRef(null);\n  const previousFocusRef = useRef(null);\n\n  useEffect(() => {\n    // Store the previously focused element\n    previousFocusRef.current = document.activeElement;\n    \n    // Focus the first input when modal opens\n    if (firstInputRef.current) {\n      firstInputRef.current.focus();\n    }\n    \n    // Prevent body scroll\n    document.body.style.overflow = 'hidden';\n    \n    // Cleanup function\n    return () => {\n      document.body.style.overflow = 'unset';\n      // Return focus to the previously focused element\n      if (previousFocusRef.current) {\n        previousFocusRef.current.focus();\n      }\n    };\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value, files } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: files ? files[0] : value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    // Simulate form submission\n    alert('Application submitted successfully! (This is a demo)');\n    onClose();\n  };\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Escape') {\n      onClose();\n    }\n    \n    // Trap focus within modal\n    if (e.key === 'Tab') {\n      const focusableElements = modalRef.current.querySelectorAll(\n        'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n      );\n      const firstElement = focusableElements[0];\n      const lastElement = focusableElements[focusableElements.length - 1];\n      \n      if (e.shiftKey && document.activeElement === firstElement) {\n        e.preventDefault();\n        lastElement.focus();\n      } else if (!e.shiftKey && document.activeElement === lastElement) {\n        e.preventDefault();\n        firstElement.focus();\n      }\n    }\n  };\n\n  const handleBackdropClick = (e) => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <div \n      className=\"modal-backdrop\" \n      onClick={handleBackdropClick}\n      onKeyDown={handleKeyDown}\n      role=\"dialog\"\n      aria-modal=\"true\"\n      aria-labelledby=\"modal-title\"\n      aria-describedby=\"modal-description\"\n    >\n      <div className=\"modal-content\" ref={modalRef}>\n        <div className=\"modal-header\">\n          <h2 id=\"modal-title\" className=\"modal-title\">\n            Apply for {job.title}\n          </h2>\n          <button\n            className=\"modal-close\"\n            onClick={onClose}\n            aria-label=\"Close application form\"\n          >\n            <span aria-hidden=\"true\">×</span>\n          </button>\n        </div>\n        \n        <div className=\"modal-body\">\n          <div id=\"modal-description\" className=\"job-summary\">\n            <p><strong>Agency:</strong> {job.agency}</p>\n            <p><strong>Location:</strong> {job.location}</p>\n            <p><strong>Salary:</strong> {job.salary}</p>\n          </div>\n          \n          <form onSubmit={handleSubmit} className=\"application-form\">\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"firstName\" className=\"form-label\">\n                  First Name <span className=\"required\" aria-label=\"required\">*</span>\n                </label>\n                <input\n                  ref={firstInputRef}\n                  id=\"firstName\"\n                  name=\"firstName\"\n                  type=\"text\"\n                  className=\"form-input\"\n                  value={formData.firstName}\n                  onChange={handleInputChange}\n                  required\n                  aria-required=\"true\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label htmlFor=\"lastName\" className=\"form-label\">\n                  Last Name <span className=\"required\" aria-label=\"required\">*</span>\n                </label>\n                <input\n                  id=\"lastName\"\n                  name=\"lastName\"\n                  type=\"text\"\n                  className=\"form-input\"\n                  value={formData.lastName}\n                  onChange={handleInputChange}\n                  required\n                  aria-required=\"true\"\n                />\n              </div>\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"email\" className=\"form-label\">\n                Email Address <span className=\"required\" aria-label=\"required\">*</span>\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                className=\"form-input\"\n                value={formData.email}\n                onChange={handleInputChange}\n                required\n                aria-required=\"true\"\n              />\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"phone\" className=\"form-label\">\n                Phone Number\n              </label>\n              <input\n                id=\"phone\"\n                name=\"phone\"\n                type=\"tel\"\n                className=\"form-input\"\n                value={formData.phone}\n                onChange={handleInputChange}\n              />\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"resume\" className=\"form-label\">\n                Resume <span className=\"required\" aria-label=\"required\">*</span>\n              </label>\n              <input\n                id=\"resume\"\n                name=\"resume\"\n                type=\"file\"\n                className=\"form-input\"\n                onChange={handleInputChange}\n                accept=\".pdf,.doc,.docx\"\n                required\n                aria-required=\"true\"\n                aria-describedby=\"resume-help\"\n              />\n              <div id=\"resume-help\" className=\"form-help\">\n                Accepted formats: PDF, DOC, DOCX (Max 5MB)\n              </div>\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"coverLetter\" className=\"form-label\">\n                Cover Letter\n              </label>\n              <textarea\n                id=\"coverLetter\"\n                name=\"coverLetter\"\n                className=\"form-input\"\n                rows=\"6\"\n                value={formData.coverLetter}\n                onChange={handleInputChange}\n                placeholder=\"Tell us why you're interested in this position...\"\n                aria-describedby=\"cover-letter-help\"\n              />\n              <div id=\"cover-letter-help\" className=\"form-help\">\n                Optional: Explain your interest and qualifications for this role\n              </div>\n            </div>\n            \n            <div className=\"modal-actions\">\n              <button type=\"button\" className=\"btn btn-secondary\" onClick={onClose}>\n                Cancel\n              </button>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                Submit Application\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ApplicationModal;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,GAAG;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAGf,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMgB,aAAa,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMiB,gBAAgB,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAErCD,SAAS,CAAC,MAAM;IACd;IACAkB,gBAAgB,CAACC,OAAO,GAAGC,QAAQ,CAACC,aAAa;;IAEjD;IACA,IAAIJ,aAAa,CAACE,OAAO,EAAE;MACzBF,aAAa,CAACE,OAAO,CAACG,KAAK,CAAC,CAAC;IAC/B;;IAEA;IACAF,QAAQ,CAACG,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;;IAEvC;IACA,OAAO,MAAM;MACXL,QAAQ,CAACG,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;MACtC;MACA,IAAIP,gBAAgB,CAACC,OAAO,EAAE;QAC5BD,gBAAgB,CAACC,OAAO,CAACG,KAAK,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAGH,CAAC,CAACI,MAAM;IACvCtB,WAAW,CAACuB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGE,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD;IAC7B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,YAAY,GAAIN,CAAC,IAAK;IAC1BA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB;IACAC,KAAK,CAAC,sDAAsD,CAAC;IAC7D7B,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM8B,aAAa,GAAIT,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACU,GAAG,KAAK,QAAQ,EAAE;MACtB/B,OAAO,CAAC,CAAC;IACX;;IAEA;IACA,IAAIqB,CAAC,CAACU,GAAG,KAAK,KAAK,EAAE;MACnB,MAAMC,iBAAiB,GAAGtB,QAAQ,CAACG,OAAO,CAACoB,gBAAgB,CACzD,0EACF,CAAC;MACD,MAAMC,YAAY,GAAGF,iBAAiB,CAAC,CAAC,CAAC;MACzC,MAAMG,WAAW,GAAGH,iBAAiB,CAACA,iBAAiB,CAACI,MAAM,GAAG,CAAC,CAAC;MAEnE,IAAIf,CAAC,CAACgB,QAAQ,IAAIvB,QAAQ,CAACC,aAAa,KAAKmB,YAAY,EAAE;QACzDb,CAAC,CAACO,cAAc,CAAC,CAAC;QAClBO,WAAW,CAACnB,KAAK,CAAC,CAAC;MACrB,CAAC,MAAM,IAAI,CAACK,CAAC,CAACgB,QAAQ,IAAIvB,QAAQ,CAACC,aAAa,KAAKoB,WAAW,EAAE;QAChEd,CAAC,CAACO,cAAc,CAAC,CAAC;QAClBM,YAAY,CAAClB,KAAK,CAAC,CAAC;MACtB;IACF;EACF,CAAC;EAED,MAAMsB,mBAAmB,GAAIjB,CAAC,IAAK;IACjC,IAAIA,CAAC,CAACI,MAAM,KAAKJ,CAAC,CAACkB,aAAa,EAAE;MAChCvC,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEH,OAAA;IACE2C,SAAS,EAAC,gBAAgB;IAC1BC,OAAO,EAAEH,mBAAoB;IAC7BI,SAAS,EAAEZ,aAAc;IACzBa,IAAI,EAAC,QAAQ;IACb,cAAW,MAAM;IACjB,mBAAgB,aAAa;IAC7B,oBAAiB,mBAAmB;IAAAC,QAAA,eAEpC/C,OAAA;MAAK2C,SAAS,EAAC,eAAe;MAACK,GAAG,EAAEnC,QAAS;MAAAkC,QAAA,gBAC3C/C,OAAA;QAAK2C,SAAS,EAAC,cAAc;QAAAI,QAAA,gBAC3B/C,OAAA;UAAIiD,EAAE,EAAC,aAAa;UAACN,SAAS,EAAC,aAAa;UAAAI,QAAA,GAAC,YACjC,EAAC7C,GAAG,CAACgD,KAAK;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACLtD,OAAA;UACE2C,SAAS,EAAC,aAAa;UACvBC,OAAO,EAAEzC,OAAQ;UACjB,cAAW,wBAAwB;UAAA4C,QAAA,eAEnC/C,OAAA;YAAM,eAAY,MAAM;YAAA+C,QAAA,EAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtD,OAAA;QAAK2C,SAAS,EAAC,YAAY;QAAAI,QAAA,gBACzB/C,OAAA;UAAKiD,EAAE,EAAC,mBAAmB;UAACN,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACjD/C,OAAA;YAAA+C,QAAA,gBAAG/C,OAAA;cAAA+C,QAAA,EAAQ;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACpD,GAAG,CAACqD,MAAM;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CtD,OAAA;YAAA+C,QAAA,gBAAG/C,OAAA;cAAA+C,QAAA,EAAQ;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACpD,GAAG,CAACsD,QAAQ;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDtD,OAAA;YAAA+C,QAAA,gBAAG/C,OAAA;cAAA+C,QAAA,EAAQ;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACpD,GAAG,CAACuD,MAAM;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eAENtD,OAAA;UAAM0D,QAAQ,EAAE5B,YAAa;UAACa,SAAS,EAAC,kBAAkB;UAAAI,QAAA,gBACxD/C,OAAA;YAAK2C,SAAS,EAAC,UAAU;YAAAI,QAAA,gBACvB/C,OAAA;cAAK2C,SAAS,EAAC,YAAY;cAAAI,QAAA,gBACzB/C,OAAA;gBAAO2D,OAAO,EAAC,WAAW;gBAAChB,SAAS,EAAC,YAAY;gBAAAI,QAAA,GAAC,aACrC,eAAA/C,OAAA;kBAAM2C,SAAS,EAAC,UAAU;kBAAC,cAAW,UAAU;kBAAAI,QAAA,EAAC;gBAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACRtD,OAAA;gBACEgD,GAAG,EAAElC,aAAc;gBACnBmC,EAAE,EAAC,WAAW;gBACdxB,IAAI,EAAC,WAAW;gBAChBmC,IAAI,EAAC,MAAM;gBACXjB,SAAS,EAAC,YAAY;gBACtBjB,KAAK,EAAErB,QAAQ,CAACE,SAAU;gBAC1BsD,QAAQ,EAAEtC,iBAAkB;gBAC5BuC,QAAQ;gBACR,iBAAc;cAAM;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtD,OAAA;cAAK2C,SAAS,EAAC,YAAY;cAAAI,QAAA,gBACzB/C,OAAA;gBAAO2D,OAAO,EAAC,UAAU;gBAAChB,SAAS,EAAC,YAAY;gBAAAI,QAAA,GAAC,YACrC,eAAA/C,OAAA;kBAAM2C,SAAS,EAAC,UAAU;kBAAC,cAAW,UAAU;kBAAAI,QAAA,EAAC;gBAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACRtD,OAAA;gBACEiD,EAAE,EAAC,UAAU;gBACbxB,IAAI,EAAC,UAAU;gBACfmC,IAAI,EAAC,MAAM;gBACXjB,SAAS,EAAC,YAAY;gBACtBjB,KAAK,EAAErB,QAAQ,CAACG,QAAS;gBACzBqD,QAAQ,EAAEtC,iBAAkB;gBAC5BuC,QAAQ;gBACR,iBAAc;cAAM;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtD,OAAA;YAAK2C,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzB/C,OAAA;cAAO2D,OAAO,EAAC,OAAO;cAAChB,SAAS,EAAC,YAAY;cAAAI,QAAA,GAAC,gBAC9B,eAAA/C,OAAA;gBAAM2C,SAAS,EAAC,UAAU;gBAAC,cAAW,UAAU;gBAAAI,QAAA,EAAC;cAAC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACRtD,OAAA;cACEiD,EAAE,EAAC,OAAO;cACVxB,IAAI,EAAC,OAAO;cACZmC,IAAI,EAAC,OAAO;cACZjB,SAAS,EAAC,YAAY;cACtBjB,KAAK,EAAErB,QAAQ,CAACI,KAAM;cACtBoD,QAAQ,EAAEtC,iBAAkB;cAC5BuC,QAAQ;cACR,iBAAc;YAAM;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAK2C,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzB/C,OAAA;cAAO2D,OAAO,EAAC,OAAO;cAAChB,SAAS,EAAC,YAAY;cAAAI,QAAA,EAAC;YAE9C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACEiD,EAAE,EAAC,OAAO;cACVxB,IAAI,EAAC,OAAO;cACZmC,IAAI,EAAC,KAAK;cACVjB,SAAS,EAAC,YAAY;cACtBjB,KAAK,EAAErB,QAAQ,CAACK,KAAM;cACtBmD,QAAQ,EAAEtC;YAAkB;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAK2C,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzB/C,OAAA;cAAO2D,OAAO,EAAC,QAAQ;cAAChB,SAAS,EAAC,YAAY;cAAAI,QAAA,GAAC,SACtC,eAAA/C,OAAA;gBAAM2C,SAAS,EAAC,UAAU;gBAAC,cAAW,UAAU;gBAAAI,QAAA,EAAC;cAAC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACRtD,OAAA;cACEiD,EAAE,EAAC,QAAQ;cACXxB,IAAI,EAAC,QAAQ;cACbmC,IAAI,EAAC,MAAM;cACXjB,SAAS,EAAC,YAAY;cACtBkB,QAAQ,EAAEtC,iBAAkB;cAC5BwC,MAAM,EAAC,iBAAiB;cACxBD,QAAQ;cACR,iBAAc,MAAM;cACpB,oBAAiB;YAAa;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACFtD,OAAA;cAAKiD,EAAE,EAAC,aAAa;cAACN,SAAS,EAAC,WAAW;cAAAI,QAAA,EAAC;YAE5C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtD,OAAA;YAAK2C,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzB/C,OAAA;cAAO2D,OAAO,EAAC,aAAa;cAAChB,SAAS,EAAC,YAAY;cAAAI,QAAA,EAAC;YAEpD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACEiD,EAAE,EAAC,aAAa;cAChBxB,IAAI,EAAC,aAAa;cAClBkB,SAAS,EAAC,YAAY;cACtBqB,IAAI,EAAC,GAAG;cACRtC,KAAK,EAAErB,QAAQ,CAACM,WAAY;cAC5BkD,QAAQ,EAAEtC,iBAAkB;cAC5B0C,WAAW,EAAC,mDAAmD;cAC/D,oBAAiB;YAAmB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACFtD,OAAA;cAAKiD,EAAE,EAAC,mBAAmB;cAACN,SAAS,EAAC,WAAW;cAAAI,QAAA,EAAC;YAElD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtD,OAAA;YAAK2C,SAAS,EAAC,eAAe;YAAAI,QAAA,gBAC5B/C,OAAA;cAAQ4D,IAAI,EAAC,QAAQ;cAACjB,SAAS,EAAC,mBAAmB;cAACC,OAAO,EAAEzC,OAAQ;cAAA4C,QAAA,EAAC;YAEtE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtD,OAAA;cAAQ4D,IAAI,EAAC,QAAQ;cAACjB,SAAS,EAAC,iBAAiB;cAAAI,QAAA,EAAC;YAElD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CArOIH,gBAAgB;AAAAiE,EAAA,GAAhBjE,gBAAgB;AAuOtB,eAAeA,gBAAgB;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}