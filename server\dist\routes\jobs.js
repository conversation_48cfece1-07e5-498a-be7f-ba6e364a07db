"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const jobs_1 = require("../data/jobs");
const router = express_1.default.Router();
router.get('/', (req, res) => {
    try {
        const { search, agency, location, type, classification, limit = '50', offset = '0' } = req.query;
        let filteredJobs = [...jobs_1.jobs];
        if (search) {
            const searchTerm = search.toLowerCase();
            filteredJobs = filteredJobs.filter(job => job.title.toLowerCase().includes(searchTerm) ||
                job.agency.toLowerCase().includes(searchTerm) ||
                job.description.toLowerCase().includes(searchTerm));
        }
        if (agency && agency !== 'All Agencies') {
            filteredJobs = filteredJobs.filter(job => job.agency.toLowerCase() === agency.toLowerCase());
        }
        if (location && location !== 'All Locations') {
            filteredJobs = filteredJobs.filter(job => job.location.toLowerCase() === location.toLowerCase());
        }
        if (type) {
            filteredJobs = filteredJobs.filter(job => job.type.toLowerCase() === type.toLowerCase());
        }
        if (classification) {
            filteredJobs = filteredJobs.filter(job => job.classification === classification);
        }
        const startIndex = parseInt(offset);
        const endIndex = startIndex + parseInt(limit);
        const paginatedJobs = filteredJobs.slice(startIndex, endIndex);
        const jobSummaries = paginatedJobs.map(job => ({
            id: job.id,
            title: job.title,
            agency: job.agency,
            location: job.location,
            closingDate: job.closingDate,
            salary: job.salary,
            type: job.type,
            classification: job.classification
        }));
        const response = {
            jobs: jobSummaries,
            pagination: {
                total: filteredJobs.length,
                limit: parseInt(limit),
                offset: parseInt(offset),
                hasMore: endIndex < filteredJobs.length
            },
            filters: {
                search: search || undefined,
                agency: agency || undefined,
                location: location || undefined,
                type: type || undefined,
                classification: classification || undefined
            }
        };
        res.json(response);
    }
    catch (error) {
        console.error('Error fetching jobs:', error);
        res.status(500).json({
            jobs: [],
            pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
            filters: {}
        });
    }
});
router.get('/:id', (req, res) => {
    try {
        const jobId = parseInt(req.params.id);
        if (isNaN(jobId)) {
            return res.status(400).json({
                job: {},
                relatedJobs: []
            });
        }
        const job = jobs_1.jobs.find(j => j.id === jobId);
        if (!job) {
            return res.status(404).json({
                job: {},
                relatedJobs: []
            });
        }
        const response = {
            job: job,
            relatedJobs: jobs_1.jobs
                .filter(j => j.id !== jobId && j.agency === job.agency)
                .slice(0, 3)
                .map(j => ({
                id: j.id,
                title: j.title,
                location: j.location,
                closingDate: j.closingDate
            }))
        };
        res.json(response);
    }
    catch (error) {
        console.error('Error fetching job details:', error);
        res.status(500).json({
            job: {},
            relatedJobs: []
        });
    }
});
router.get('/meta/agencies', (req, res) => {
    try {
        const agencies = [...new Set(jobs_1.jobs.map(job => job.agency))].sort();
        res.json({ agencies });
    }
    catch (error) {
        res.status(500).json({
            agencies: []
        });
    }
});
router.get('/meta/locations', (req, res) => {
    try {
        const locations = [...new Set(jobs_1.jobs.map(job => job.location))].sort();
        res.json({ locations });
    }
    catch (error) {
        res.status(500).json({
            locations: []
        });
    }
});
exports.default = router;
//# sourceMappingURL=jobs.js.map