# WA Government Jobs Board - TypeScript & Docker

This project has been successfully converted from vanilla JavaScript to TypeScript and configured to run with Docker Compose.

## 🚀 Features

- **API Server**: Express.js with TypeScript
- **Client**: React with TypeScript
- **Docker Compose**: Containerized development environment
- **Type Safety**: Full TypeScript implementation with strict type checking
- **Health Checks**: API health monitoring
- **Hot Reload**: Development mode with live reloading

## 📁 Project Structure

```
├── server/                 # Express API (TypeScript)
│   ├── src/
│   │   ├── types/         # TypeScript type definitions
│   │   ├── routes/        # API routes
│   │   ├── data/          # Mock data
│   │   └── server.ts      # Main server file
│   ├── dist/              # Compiled JavaScript (generated)
│   ├── Dockerfile
│   ├── package.json
│   └── tsconfig.json
├── client/                # React app (TypeScript)
│   ├── src/
│   │   ├── components/    # React components (.tsx)
│   │   ├── types/         # TypeScript type definitions
│   │   └── data/          # Mock data
│   ├── Dockerfile
│   ├── package.json
│   └── tsconfig.json
└── docker-compose.yaml
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+
- Docker & Docker Compose
- npm

### Local Development

1. **Install dependencies:**
   ```bash
   # Server
   cd server
   npm install

   # Client
   cd ../client
   npm install
   ```

2. **Run locally:**
   ```bash
   # Terminal 1 - API Server
   cd server
   npm run build  # Build TypeScript
   npm start      # Start server on port 3001

   # Terminal 2 - React Client
   cd client
   npm start      # Start client (will prompt for different port if 3000 is taken)
   ```

### Docker Compose

1. **Build and run with Docker:**
   ```bash
   docker-compose up --build
   ```

2. **Access the applications:**
   - Client: http://localhost:3000
   - API: http://localhost:3001
   - Health Check: http://localhost:3001/health

## 🔧 TypeScript Configuration

### Server (Express API)
- Strict TypeScript configuration
- Type definitions for all Express routes
- Comprehensive error handling with typed responses
- Type-safe data models

### Client (React)
- React with TypeScript
- Typed component props and state
- Type-safe event handlers
- Strict null checks enabled

## 📋 Available Scripts

### Server
- `npm start` - Run compiled JavaScript
- `npm run dev` - Development mode with ts-node
- `npm run build` - Compile TypeScript to JavaScript
- `npm run type-check` - Type checking without compilation

### Client
- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests
- `npm run type-check` - Type checking without compilation

## 🐳 Docker Configuration

### API Service
- Node.js 18 Alpine base image
- TypeScript compilation during build
- Health checks enabled
- Development volume mounting for hot reload

### Client Service
- Node.js 18 Alpine base image
- React development server
- Hot reloading enabled
- Depends on API service health

## 🔍 API Endpoints

- `GET /` - API information
- `GET /health` - Health check
- `GET /api/jobs` - List all jobs with filtering
- `GET /api/jobs/:id` - Get specific job details
- `GET /api/jobs/meta/agencies` - Get list of agencies
- `GET /api/jobs/meta/locations` - Get list of locations
- `POST /api/apply` - Submit job application
- `GET /api/apply/status/:applicationNumber` - Check application status
- `GET /api/apply/stats` - Application statistics

## ✅ Migration Completed

- ✅ Server converted to TypeScript
- ✅ Client converted to TypeScript
- ✅ Docker Compose configuration
- ✅ Type definitions for all components
- ✅ Dockerfiles for both services
- ✅ Build scripts updated
- ✅ Development and production configurations

## 🧪 Testing

Both applications compile successfully with TypeScript and run without errors. The API serves data correctly and the React client renders properly with full type safety.

## 🚀 Next Steps

1. Add unit tests with Jest and TypeScript
2. Set up CI/CD pipeline
3. Add environment-specific configurations
4. Implement database integration
5. Add authentication and authorization