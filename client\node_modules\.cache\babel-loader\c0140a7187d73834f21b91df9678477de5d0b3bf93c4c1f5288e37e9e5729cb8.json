{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\dev\\\\saas_jobseeker\\\\client\\\\src\\\\components\\\\Hero.tsx\";\nimport React from 'react';\nimport './Hero.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"hero\",\n    \"aria-labelledby\": \"hero-title\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          id: \"hero-title\",\n          className: \"hero-title\",\n          children: \"Find Your Next Career with WA Government\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hero-description\",\n          children: \"Discover meaningful career opportunities across Western Australia's public sector. Join us in serving the community and building a better future for all Western Australians.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-number\",\n              \"aria-label\": \"Over 500 current job openings\",\n              children: \"500+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Current Openings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-number\",\n              \"aria-label\": \"Over 50 government agencies\",\n              children: \"50+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Government Agencies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-number\",\n              \"aria-label\": \"Statewide opportunities\",\n              children: \"Statewide\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Opportunities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Hero", "className", "children", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/dev/saas_jobseeker/client/src/components/Hero.tsx"], "sourcesContent": ["import React from 'react';\nimport './Hero.css';\n\nconst Hero: React.FC = () => {\n  return (\n    <section className=\"hero\" aria-labelledby=\"hero-title\">\n      <div className=\"container\">\n        <div className=\"hero-content\">\n          <h2 id=\"hero-title\" className=\"hero-title\">\n            Find Your Next Career with WA Government\n          </h2>\n          <p className=\"hero-description\">\n            Discover meaningful career opportunities across Western Australia's public sector. \n            Join us in serving the community and building a better future for all Western Australians.\n          </p>\n          <div className=\"hero-stats\">\n            <div className=\"stat\">\n              <span className=\"stat-number\" aria-label=\"Over 500 current job openings\">500+</span>\n              <span className=\"stat-label\">Current Openings</span>\n            </div>\n            <div className=\"stat\">\n              <span className=\"stat-number\" aria-label=\"Over 50 government agencies\">50+</span>\n              <span className=\"stat-label\">Government Agencies</span>\n            </div>\n            <div className=\"stat\">\n              <span className=\"stat-number\" aria-label=\"Statewide opportunities\">Statewide</span>\n              <span className=\"stat-label\">Opportunities</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAC3B,oBACED,OAAA;IAASE,SAAS,EAAC,MAAM;IAAC,mBAAgB,YAAY;IAAAC,QAAA,eACpDH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBH,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAII,EAAE,EAAC,YAAY;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAE3C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLR,OAAA;UAAGE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAGhC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJR,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBH,OAAA;YAAKE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBH,OAAA;cAAME,SAAS,EAAC,aAAa;cAAC,cAAW,+BAA+B;cAAAC,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpFR,OAAA;cAAME,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNR,OAAA;YAAKE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBH,OAAA;cAAME,SAAS,EAAC,aAAa;cAAC,cAAW,6BAA6B;cAAAC,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjFR,OAAA;cAAME,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNR,OAAA;YAAKE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBH,OAAA;cAAME,SAAS,EAAC,aAAa;cAAC,cAAW,yBAAyB;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnFR,OAAA;cAAME,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACC,EAAA,GA9BIR,IAAc;AAgCpB,eAAeA,IAAI;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}