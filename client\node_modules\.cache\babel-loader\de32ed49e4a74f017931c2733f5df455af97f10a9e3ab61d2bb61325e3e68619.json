{"ast": null, "code": "export const mockJobs = [{\n  id: 1,\n  title: \"Senior Policy Advisor\",\n  agency: \"Department of Premier and Cabinet\",\n  location: \"Perth\",\n  closingDate: \"2024-02-15\",\n  salary: \"$85,000 - $95,000\",\n  type: \"Permanent\",\n  description: \"Lead policy development and provide strategic advice on government initiatives.\"\n}, {\n  id: 2,\n  title: \"Environmental Scientist\",\n  agency: \"Department of Water and Environmental Regulation\",\n  location: \"Perth\",\n  closingDate: \"2024-02-20\",\n  salary: \"$70,000 - $80,000\",\n  type: \"Contract\",\n  description: \"Conduct environmental assessments and monitoring programs.\"\n}, {\n  id: 3,\n  title: \"Education Support Officer\",\n  agency: \"Department of Education\",\n  location: \"Bunbury\",\n  closingDate: \"2024-02-25\",\n  salary: \"$55,000 - $65,000\",\n  type: \"Permanent\",\n  description: \"Provide administrative and educational support in school environments.\"\n}, {\n  id: 4,\n  title: \"IT Systems Analyst\",\n  agency: \"Department of Finance\",\n  location: \"Perth\",\n  closingDate: \"2024-03-01\",\n  salary: \"$75,000 - $85,000\",\n  type: \"Permanent\",\n  description: \"Analyze and improve government IT systems and processes.\"\n}, {\n  id: 5,\n  title: \"Health Promotion Officer\",\n  agency: \"Department of Health\",\n  location: \"Geraldton\",\n  closingDate: \"2024-03-05\",\n  salary: \"$65,000 - $75,000\",\n  type: \"Contract\",\n  description: \"Develop and implement community health promotion programs.\"\n}, {\n  id: 6,\n  title: \"Project Manager\",\n  agency: \"Department of Transport\",\n  location: \"Perth\",\n  closingDate: \"2024-03-10\",\n  salary: \"$90,000 - $100,000\",\n  type: \"Permanent\",\n  description: \"Manage infrastructure projects and coordinate stakeholder engagement.\"\n}];\nexport const agencies = [\"All Agencies\", \"Department of Premier and Cabinet\", \"Department of Water and Environmental Regulation\", \"Department of Education\", \"Department of Finance\", \"Department of Health\", \"Department of Transport\"];\nexport const locations = [\"All Locations\", \"Perth\", \"Bunbury\", \"Geraldton\", \"Albany\", \"Kalgoorlie\"];", "map": {"version": 3, "names": ["mockJobs", "id", "title", "agency", "location", "closingDate", "salary", "type", "description", "agencies", "locations"], "sources": ["C:/Users/<USER>/Documents/dev/saas_jobseeker/client/src/data/mockJobs.ts"], "sourcesContent": ["import { Job } from '../types';\n\nexport const mockJobs: Job[] = [\n  {\n    id: 1,\n    title: \"Senior Policy Advisor\",\n    agency: \"Department of Premier and Cabinet\",\n    location: \"Perth\",\n    closingDate: \"2024-02-15\",\n    salary: \"$85,000 - $95,000\",\n    type: \"Permanent\",\n    description: \"Lead policy development and provide strategic advice on government initiatives.\"\n  },\n  {\n    id: 2,\n    title: \"Environmental Scientist\",\n    agency: \"Department of Water and Environmental Regulation\",\n    location: \"Perth\",\n    closingDate: \"2024-02-20\",\n    salary: \"$70,000 - $80,000\",\n    type: \"Contract\",\n    description: \"Conduct environmental assessments and monitoring programs.\"\n  },\n  {\n    id: 3,\n    title: \"Education Support Officer\",\n    agency: \"Department of Education\",\n    location: \"Bunbury\",\n    closingDate: \"2024-02-25\",\n    salary: \"$55,000 - $65,000\",\n    type: \"Permanent\",\n    description: \"Provide administrative and educational support in school environments.\"\n  },\n  {\n    id: 4,\n    title: \"IT Systems Analyst\",\n    agency: \"Department of Finance\",\n    location: \"Perth\",\n    closingDate: \"2024-03-01\",\n    salary: \"$75,000 - $85,000\",\n    type: \"Permanent\",\n    description: \"Analyze and improve government IT systems and processes.\"\n  },\n  {\n    id: 5,\n    title: \"Health Promotion Officer\",\n    agency: \"Department of Health\",\n    location: \"Geraldton\",\n    closingDate: \"2024-03-05\",\n    salary: \"$65,000 - $75,000\",\n    type: \"Contract\",\n    description: \"Develop and implement community health promotion programs.\"\n  },\n  {\n    id: 6,\n    title: \"Project Manager\",\n    agency: \"Department of Transport\",\n    location: \"Perth\",\n    closingDate: \"2024-03-10\",\n    salary: \"$90,000 - $100,000\",\n    type: \"Permanent\",\n    description: \"Manage infrastructure projects and coordinate stakeholder engagement.\"\n  }\n];\n\nexport const agencies: string[] = [\n  \"All Agencies\",\n  \"Department of Premier and Cabinet\",\n  \"Department of Water and Environmental Regulation\",\n  \"Department of Education\",\n  \"Department of Finance\",\n  \"Department of Health\",\n  \"Department of Transport\"\n];\n\nexport const locations: string[] = [\n  \"All Locations\",\n  \"Perth\",\n  \"Bunbury\",\n  \"Geraldton\",\n  \"Albany\",\n  \"Kalgoorlie\"\n];"], "mappings": "AAEA,OAAO,MAAMA,QAAe,GAAG,CAC7B;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,uBAAuB;EAC9BC,MAAM,EAAE,mCAAmC;EAC3CC,QAAQ,EAAE,OAAO;EACjBC,WAAW,EAAE,YAAY;EACzBC,MAAM,EAAE,mBAAmB;EAC3BC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,yBAAyB;EAChCC,MAAM,EAAE,kDAAkD;EAC1DC,QAAQ,EAAE,OAAO;EACjBC,WAAW,EAAE,YAAY;EACzBC,MAAM,EAAE,mBAAmB;EAC3BC,IAAI,EAAE,UAAU;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,2BAA2B;EAClCC,MAAM,EAAE,yBAAyB;EACjCC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,YAAY;EACzBC,MAAM,EAAE,mBAAmB;EAC3BC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,oBAAoB;EAC3BC,MAAM,EAAE,uBAAuB;EAC/BC,QAAQ,EAAE,OAAO;EACjBC,WAAW,EAAE,YAAY;EACzBC,MAAM,EAAE,mBAAmB;EAC3BC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,0BAA0B;EACjCC,MAAM,EAAE,sBAAsB;EAC9BC,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,YAAY;EACzBC,MAAM,EAAE,mBAAmB;EAC3BC,IAAI,EAAE,UAAU;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,iBAAiB;EACxBC,MAAM,EAAE,yBAAyB;EACjCC,QAAQ,EAAE,OAAO;EACjBC,WAAW,EAAE,YAAY;EACzBC,MAAM,EAAE,oBAAoB;EAC5BC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE;AACf,CAAC,CACF;AAED,OAAO,MAAMC,QAAkB,GAAG,CAChC,cAAc,EACd,mCAAmC,EACnC,kDAAkD,EAClD,yBAAyB,EACzB,uBAAuB,EACvB,sBAAsB,EACtB,yBAAyB,CAC1B;AAED,OAAO,MAAMC,SAAmB,GAAG,CACjC,eAAe,EACf,OAAO,EACP,SAAS,EACT,WAAW,EACX,QAAQ,EACR,YAAY,CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}