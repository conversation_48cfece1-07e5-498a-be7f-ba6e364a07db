{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\dev\\\\saas_jobseeker\\\\client\\\\src\\\\components\\\\ApplicationModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ApplicationModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ApplicationModal = ({\n  job,\n  onClose\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    jobId: job.id,\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    coverLetter: '',\n    resumeFile: null\n  });\n  const modalRef = useRef(null);\n  const firstInputRef = useRef(null);\n  const previousFocusRef = useRef(null);\n  useEffect(() => {\n    // Store the previously focused element\n    previousFocusRef.current = document.activeElement;\n\n    // Focus the first input when modal opens\n    if (firstInputRef.current) {\n      firstInputRef.current.focus();\n    }\n\n    // Prevent body scroll\n    document.body.style.overflow = 'hidden';\n\n    // Cleanup function\n    return () => {\n      document.body.style.overflow = 'unset';\n      // Return focus to the previously focused element\n      if (previousFocusRef.current) {\n        previousFocusRef.current.focus();\n      }\n    };\n  }, []);\n  const handleInputChange = e => {\n    const target = e.target;\n    const {\n      name,\n      value,\n      files\n    } = target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: files ? files[0] : value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    // Simulate form submission\n    alert('Application submitted successfully! (This is a demo)');\n    onClose();\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Escape') {\n      onClose();\n    }\n\n    // Trap focus within modal\n    if (e.key === 'Tab' && modalRef.current) {\n      const focusableElements = modalRef.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n      const firstElement = focusableElements[0];\n      const lastElement = focusableElements[focusableElements.length - 1];\n      if (e.shiftKey && document.activeElement === firstElement) {\n        e.preventDefault();\n        lastElement === null || lastElement === void 0 ? void 0 : lastElement.focus();\n      } else if (!e.shiftKey && document.activeElement === lastElement) {\n        e.preventDefault();\n        firstElement === null || firstElement === void 0 ? void 0 : firstElement.focus();\n      }\n    }\n  };\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-backdrop\",\n    onClick: handleBackdropClick,\n    onKeyDown: handleKeyDown,\n    role: \"dialog\",\n    \"aria-modal\": \"true\",\n    \"aria-labelledby\": \"modal-title\",\n    \"aria-describedby\": \"modal-description\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      ref: modalRef,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          id: \"modal-title\",\n          className: \"modal-title\",\n          children: [\"Apply for \", job.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: onClose,\n          \"aria-label\": \"Close application form\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            \"aria-hidden\": \"true\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"modal-description\",\n          className: \"job-summary\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Agency:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 16\n            }, this), \" \", job.agency]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Location:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 16\n            }, this), \" \", job.location]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Salary:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 16\n            }, this), \" \", job.salary]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"application-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"firstName\",\n                className: \"form-label\",\n                children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"required\",\n                  \"aria-label\": \"required\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                ref: firstInputRef,\n                id: \"firstName\",\n                name: \"firstName\",\n                type: \"text\",\n                className: \"form-input\",\n                value: formData.firstName,\n                onChange: handleInputChange,\n                required: true,\n                \"aria-required\": \"true\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"lastName\",\n                className: \"form-label\",\n                children: [\"Last Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"required\",\n                  \"aria-label\": \"required\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"lastName\",\n                name: \"lastName\",\n                type: \"text\",\n                className: \"form-input\",\n                value: formData.lastName,\n                onChange: handleInputChange,\n                required: true,\n                \"aria-required\": \"true\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"form-label\",\n              children: [\"Email Address \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                \"aria-label\": \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 31\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"email\",\n              name: \"email\",\n              type: \"email\",\n              className: \"form-input\",\n              value: formData.email,\n              onChange: handleInputChange,\n              required: true,\n              \"aria-required\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phone\",\n              className: \"form-label\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"phone\",\n              name: \"phone\",\n              type: \"tel\",\n              className: \"form-input\",\n              value: formData.phone,\n              onChange: handleInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"resume\",\n              className: \"form-label\",\n              children: [\"Resume \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                \"aria-label\": \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 24\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"resume\",\n              name: \"resume\",\n              type: \"file\",\n              className: \"form-input\",\n              onChange: handleInputChange,\n              accept: \".pdf,.doc,.docx\",\n              required: true,\n              \"aria-required\": \"true\",\n              \"aria-describedby\": \"resume-help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"resume-help\",\n              className: \"form-help\",\n              children: \"Accepted formats: PDF, DOC, DOCX (Max 5MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"coverLetter\",\n              className: \"form-label\",\n              children: \"Cover Letter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"coverLetter\",\n              name: \"coverLetter\",\n              className: \"form-input\",\n              rows: 6,\n              value: formData.coverLetter,\n              onChange: handleInputChange,\n              placeholder: \"Tell us why you're interested in this position...\",\n              \"aria-describedby\": \"cover-letter-help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"cover-letter-help\",\n              className: \"form-help\",\n              children: \"Optional: Explain your interest and qualifications for this role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: onClose,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              children: \"Submit Application\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(ApplicationModal, \"e9oF5a9lXEEzW3BjmXtjofnLv2Y=\");\n_c = ApplicationModal;\nexport default ApplicationModal;\nvar _c;\n$RefreshReg$(_c, \"ApplicationModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ApplicationModal", "job", "onClose", "_s", "formData", "setFormData", "jobId", "id", "firstName", "lastName", "email", "phone", "coverLetter", "resumeFile", "modalRef", "firstInputRef", "previousFocusRef", "current", "document", "activeElement", "focus", "body", "style", "overflow", "handleInputChange", "e", "target", "name", "value", "files", "prev", "handleSubmit", "preventDefault", "alert", "handleKeyDown", "key", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "length", "shift<PERSON>ey", "handleBackdropClick", "currentTarget", "className", "onClick", "onKeyDown", "role", "children", "ref", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "agency", "location", "salary", "onSubmit", "htmlFor", "type", "onChange", "required", "accept", "rows", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/dev/saas_jobseeker/client/src/components/ApplicationModal.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { ApplicationModalProps, ApplicationData } from '../types';\nimport './ApplicationModal.css';\n\nconst ApplicationModal: React.FC<ApplicationModalProps> = ({ job, onClose }) => {\n  const [formData, setFormData] = useState<ApplicationData>({\n    jobId: job.id,\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    coverLetter: '',\n    resumeFile: null\n  });\n\n  const modalRef = useRef<HTMLDivElement>(null);\n  const firstInputRef = useRef<HTMLInputElement>(null);\n  const previousFocusRef = useRef<HTMLElement | null>(null);\n\n  useEffect(() => {\n    // Store the previously focused element\n    previousFocusRef.current = document.activeElement as HTMLElement;\n    \n    // Focus the first input when modal opens\n    if (firstInputRef.current) {\n      firstInputRef.current.focus();\n    }\n    \n    // Prevent body scroll\n    document.body.style.overflow = 'hidden';\n    \n    // Cleanup function\n    return () => {\n      document.body.style.overflow = 'unset';\n      // Return focus to the previously focused element\n      if (previousFocusRef.current) {\n        previousFocusRef.current.focus();\n      }\n    };\n  }, []);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>): void => {\n    const target = e.target as HTMLInputElement;\n    const { name, value, files } = target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: files ? files[0] : value\n    }));\n  };\n\n  const handleSubmit = (e: React.FormEvent): void => {\n    e.preventDefault();\n    // Simulate form submission\n    alert('Application submitted successfully! (This is a demo)');\n    onClose();\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent): void => {\n    if (e.key === 'Escape') {\n      onClose();\n    }\n    \n    // Trap focus within modal\n    if (e.key === 'Tab' && modalRef.current) {\n      const focusableElements = modalRef.current.querySelectorAll(\n        'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n      );\n      const firstElement = focusableElements[0] as HTMLElement;\n      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\n\n      if (e.shiftKey && document.activeElement === firstElement) {\n        e.preventDefault();\n        lastElement?.focus();\n      } else if (!e.shiftKey && document.activeElement === lastElement) {\n        e.preventDefault();\n        firstElement?.focus();\n      }\n    }\n  };\n\n  const handleBackdropClick = (e: React.MouseEvent): void => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <div \n      className=\"modal-backdrop\" \n      onClick={handleBackdropClick}\n      onKeyDown={handleKeyDown}\n      role=\"dialog\"\n      aria-modal=\"true\"\n      aria-labelledby=\"modal-title\"\n      aria-describedby=\"modal-description\"\n    >\n      <div className=\"modal-content\" ref={modalRef}>\n        <div className=\"modal-header\">\n          <h2 id=\"modal-title\" className=\"modal-title\">\n            Apply for {job.title}\n          </h2>\n          <button\n            className=\"modal-close\"\n            onClick={onClose}\n            aria-label=\"Close application form\"\n          >\n            <span aria-hidden=\"true\">×</span>\n          </button>\n        </div>\n        \n        <div className=\"modal-body\">\n          <div id=\"modal-description\" className=\"job-summary\">\n            <p><strong>Agency:</strong> {job.agency}</p>\n            <p><strong>Location:</strong> {job.location}</p>\n            <p><strong>Salary:</strong> {job.salary}</p>\n          </div>\n          \n          <form onSubmit={handleSubmit} className=\"application-form\">\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"firstName\" className=\"form-label\">\n                  First Name <span className=\"required\" aria-label=\"required\">*</span>\n                </label>\n                <input\n                  ref={firstInputRef}\n                  id=\"firstName\"\n                  name=\"firstName\"\n                  type=\"text\"\n                  className=\"form-input\"\n                  value={formData.firstName}\n                  onChange={handleInputChange}\n                  required\n                  aria-required=\"true\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label htmlFor=\"lastName\" className=\"form-label\">\n                  Last Name <span className=\"required\" aria-label=\"required\">*</span>\n                </label>\n                <input\n                  id=\"lastName\"\n                  name=\"lastName\"\n                  type=\"text\"\n                  className=\"form-input\"\n                  value={formData.lastName}\n                  onChange={handleInputChange}\n                  required\n                  aria-required=\"true\"\n                />\n              </div>\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"email\" className=\"form-label\">\n                Email Address <span className=\"required\" aria-label=\"required\">*</span>\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                className=\"form-input\"\n                value={formData.email}\n                onChange={handleInputChange}\n                required\n                aria-required=\"true\"\n              />\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"phone\" className=\"form-label\">\n                Phone Number\n              </label>\n              <input\n                id=\"phone\"\n                name=\"phone\"\n                type=\"tel\"\n                className=\"form-input\"\n                value={formData.phone}\n                onChange={handleInputChange}\n              />\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"resume\" className=\"form-label\">\n                Resume <span className=\"required\" aria-label=\"required\">*</span>\n              </label>\n              <input\n                id=\"resume\"\n                name=\"resume\"\n                type=\"file\"\n                className=\"form-input\"\n                onChange={handleInputChange}\n                accept=\".pdf,.doc,.docx\"\n                required\n                aria-required=\"true\"\n                aria-describedby=\"resume-help\"\n              />\n              <div id=\"resume-help\" className=\"form-help\">\n                Accepted formats: PDF, DOC, DOCX (Max 5MB)\n              </div>\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"coverLetter\" className=\"form-label\">\n                Cover Letter\n              </label>\n              <textarea\n                id=\"coverLetter\"\n                name=\"coverLetter\"\n                className=\"form-input\"\n                rows={6}\n                value={formData.coverLetter}\n                onChange={handleInputChange}\n                placeholder=\"Tell us why you're interested in this position...\"\n                aria-describedby=\"cover-letter-help\"\n              />\n              <div id=\"cover-letter-help\" className=\"form-help\">\n                Optional: Explain your interest and qualifications for this role\n              </div>\n            </div>\n            \n            <div className=\"modal-actions\">\n              <button type=\"button\" className=\"btn btn-secondary\" onClick={onClose}>\n                Cancel\n              </button>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                Submit Application\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ApplicationModal;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAE1D,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC,GAAG;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAkB;IACxDW,KAAK,EAAEL,GAAG,CAACM,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAGjB,MAAM,CAAiB,IAAI,CAAC;EAC7C,MAAMkB,aAAa,GAAGlB,MAAM,CAAmB,IAAI,CAAC;EACpD,MAAMmB,gBAAgB,GAAGnB,MAAM,CAAqB,IAAI,CAAC;EAEzDD,SAAS,CAAC,MAAM;IACd;IACAoB,gBAAgB,CAACC,OAAO,GAAGC,QAAQ,CAACC,aAA4B;;IAEhE;IACA,IAAIJ,aAAa,CAACE,OAAO,EAAE;MACzBF,aAAa,CAACE,OAAO,CAACG,KAAK,CAAC,CAAC;IAC/B;;IAEA;IACAF,QAAQ,CAACG,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;;IAEvC;IACA,OAAO,MAAM;MACXL,QAAQ,CAACG,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;MACtC;MACA,IAAIP,gBAAgB,CAACC,OAAO,EAAE;QAC5BD,gBAAgB,CAACC,OAAO,CAACG,KAAK,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,iBAAiB,GAAIC,CAA4D,IAAW;IAChG,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAA0B;IAC3C,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAGH,MAAM;IACrCrB,WAAW,CAACyB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGE,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD;IAC7B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIN,CAAkB,IAAW;IACjDA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB;IACAC,KAAK,CAAC,sDAAsD,CAAC;IAC7D/B,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMgC,aAAa,GAAIT,CAAsB,IAAW;IACtD,IAAIA,CAAC,CAACU,GAAG,KAAK,QAAQ,EAAE;MACtBjC,OAAO,CAAC,CAAC;IACX;;IAEA;IACA,IAAIuB,CAAC,CAACU,GAAG,KAAK,KAAK,IAAIrB,QAAQ,CAACG,OAAO,EAAE;MACvC,MAAMmB,iBAAiB,GAAGtB,QAAQ,CAACG,OAAO,CAACoB,gBAAgB,CACzD,0EACF,CAAC;MACD,MAAMC,YAAY,GAAGF,iBAAiB,CAAC,CAAC,CAAgB;MACxD,MAAMG,WAAW,GAAGH,iBAAiB,CAACA,iBAAiB,CAACI,MAAM,GAAG,CAAC,CAAgB;MAElF,IAAIf,CAAC,CAACgB,QAAQ,IAAIvB,QAAQ,CAACC,aAAa,KAAKmB,YAAY,EAAE;QACzDb,CAAC,CAACO,cAAc,CAAC,CAAC;QAClBO,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEnB,KAAK,CAAC,CAAC;MACtB,CAAC,MAAM,IAAI,CAACK,CAAC,CAACgB,QAAQ,IAAIvB,QAAQ,CAACC,aAAa,KAAKoB,WAAW,EAAE;QAChEd,CAAC,CAACO,cAAc,CAAC,CAAC;QAClBM,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAElB,KAAK,CAAC,CAAC;MACvB;IACF;EACF,CAAC;EAED,MAAMsB,mBAAmB,GAAIjB,CAAmB,IAAW;IACzD,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACkB,aAAa,EAAE;MAChCzC,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEH,OAAA;IACE6C,SAAS,EAAC,gBAAgB;IAC1BC,OAAO,EAAEH,mBAAoB;IAC7BI,SAAS,EAAEZ,aAAc;IACzBa,IAAI,EAAC,QAAQ;IACb,cAAW,MAAM;IACjB,mBAAgB,aAAa;IAC7B,oBAAiB,mBAAmB;IAAAC,QAAA,eAEpCjD,OAAA;MAAK6C,SAAS,EAAC,eAAe;MAACK,GAAG,EAAEnC,QAAS;MAAAkC,QAAA,gBAC3CjD,OAAA;QAAK6C,SAAS,EAAC,cAAc;QAAAI,QAAA,gBAC3BjD,OAAA;UAAIQ,EAAE,EAAC,aAAa;UAACqC,SAAS,EAAC,aAAa;UAAAI,QAAA,GAAC,YACjC,EAAC/C,GAAG,CAACiD,KAAK;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACLvD,OAAA;UACE6C,SAAS,EAAC,aAAa;UACvBC,OAAO,EAAE3C,OAAQ;UACjB,cAAW,wBAAwB;UAAA8C,QAAA,eAEnCjD,OAAA;YAAM,eAAY,MAAM;YAAAiD,QAAA,EAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENvD,OAAA;QAAK6C,SAAS,EAAC,YAAY;QAAAI,QAAA,gBACzBjD,OAAA;UAAKQ,EAAE,EAAC,mBAAmB;UAACqC,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACjDjD,OAAA;YAAAiD,QAAA,gBAAGjD,OAAA;cAAAiD,QAAA,EAAQ;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrD,GAAG,CAACsD,MAAM;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CvD,OAAA;YAAAiD,QAAA,gBAAGjD,OAAA;cAAAiD,QAAA,EAAQ;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrD,GAAG,CAACuD,QAAQ;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDvD,OAAA;YAAAiD,QAAA,gBAAGjD,OAAA;cAAAiD,QAAA,EAAQ;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrD,GAAG,CAACwD,MAAM;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eAENvD,OAAA;UAAM2D,QAAQ,EAAE3B,YAAa;UAACa,SAAS,EAAC,kBAAkB;UAAAI,QAAA,gBACxDjD,OAAA;YAAK6C,SAAS,EAAC,UAAU;YAAAI,QAAA,gBACvBjD,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAI,QAAA,gBACzBjD,OAAA;gBAAO4D,OAAO,EAAC,WAAW;gBAACf,SAAS,EAAC,YAAY;gBAAAI,QAAA,GAAC,aACrC,eAAAjD,OAAA;kBAAM6C,SAAS,EAAC,UAAU;kBAAC,cAAW,UAAU;kBAAAI,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACRvD,OAAA;gBACEkD,GAAG,EAAElC,aAAc;gBACnBR,EAAE,EAAC,WAAW;gBACdoB,IAAI,EAAC,WAAW;gBAChBiC,IAAI,EAAC,MAAM;gBACXhB,SAAS,EAAC,YAAY;gBACtBhB,KAAK,EAAExB,QAAQ,CAACI,SAAU;gBAC1BqD,QAAQ,EAAErC,iBAAkB;gBAC5BsC,QAAQ;gBACR,iBAAc;cAAM;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvD,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAI,QAAA,gBACzBjD,OAAA;gBAAO4D,OAAO,EAAC,UAAU;gBAACf,SAAS,EAAC,YAAY;gBAAAI,QAAA,GAAC,YACrC,eAAAjD,OAAA;kBAAM6C,SAAS,EAAC,UAAU;kBAAC,cAAW,UAAU;kBAAAI,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACRvD,OAAA;gBACEQ,EAAE,EAAC,UAAU;gBACboB,IAAI,EAAC,UAAU;gBACfiC,IAAI,EAAC,MAAM;gBACXhB,SAAS,EAAC,YAAY;gBACtBhB,KAAK,EAAExB,QAAQ,CAACK,QAAS;gBACzBoD,QAAQ,EAAErC,iBAAkB;gBAC5BsC,QAAQ;gBACR,iBAAc;cAAM;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvD,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBjD,OAAA;cAAO4D,OAAO,EAAC,OAAO;cAACf,SAAS,EAAC,YAAY;cAAAI,QAAA,GAAC,gBAC9B,eAAAjD,OAAA;gBAAM6C,SAAS,EAAC,UAAU;gBAAC,cAAW,UAAU;gBAAAI,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACRvD,OAAA;cACEQ,EAAE,EAAC,OAAO;cACVoB,IAAI,EAAC,OAAO;cACZiC,IAAI,EAAC,OAAO;cACZhB,SAAS,EAAC,YAAY;cACtBhB,KAAK,EAAExB,QAAQ,CAACM,KAAM;cACtBmD,QAAQ,EAAErC,iBAAkB;cAC5BsC,QAAQ;cACR,iBAAc;YAAM;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvD,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBjD,OAAA;cAAO4D,OAAO,EAAC,OAAO;cAACf,SAAS,EAAC,YAAY;cAAAI,QAAA,EAAC;YAE9C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvD,OAAA;cACEQ,EAAE,EAAC,OAAO;cACVoB,IAAI,EAAC,OAAO;cACZiC,IAAI,EAAC,KAAK;cACVhB,SAAS,EAAC,YAAY;cACtBhB,KAAK,EAAExB,QAAQ,CAACO,KAAM;cACtBkD,QAAQ,EAAErC;YAAkB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvD,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBjD,OAAA;cAAO4D,OAAO,EAAC,QAAQ;cAACf,SAAS,EAAC,YAAY;cAAAI,QAAA,GAAC,SACtC,eAAAjD,OAAA;gBAAM6C,SAAS,EAAC,UAAU;gBAAC,cAAW,UAAU;gBAAAI,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACRvD,OAAA;cACEQ,EAAE,EAAC,QAAQ;cACXoB,IAAI,EAAC,QAAQ;cACbiC,IAAI,EAAC,MAAM;cACXhB,SAAS,EAAC,YAAY;cACtBiB,QAAQ,EAAErC,iBAAkB;cAC5BuC,MAAM,EAAC,iBAAiB;cACxBD,QAAQ;cACR,iBAAc,MAAM;cACpB,oBAAiB;YAAa;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACFvD,OAAA;cAAKQ,EAAE,EAAC,aAAa;cAACqC,SAAS,EAAC,WAAW;cAAAI,QAAA,EAAC;YAE5C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvD,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBjD,OAAA;cAAO4D,OAAO,EAAC,aAAa;cAACf,SAAS,EAAC,YAAY;cAAAI,QAAA,EAAC;YAEpD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvD,OAAA;cACEQ,EAAE,EAAC,aAAa;cAChBoB,IAAI,EAAC,aAAa;cAClBiB,SAAS,EAAC,YAAY;cACtBoB,IAAI,EAAE,CAAE;cACRpC,KAAK,EAAExB,QAAQ,CAACQ,WAAY;cAC5BiD,QAAQ,EAAErC,iBAAkB;cAC5ByC,WAAW,EAAC,mDAAmD;cAC/D,oBAAiB;YAAmB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACFvD,OAAA;cAAKQ,EAAE,EAAC,mBAAmB;cAACqC,SAAS,EAAC,WAAW;cAAAI,QAAA,EAAC;YAElD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvD,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAI,QAAA,gBAC5BjD,OAAA;cAAQ6D,IAAI,EAAC,QAAQ;cAAChB,SAAS,EAAC,mBAAmB;cAACC,OAAO,EAAE3C,OAAQ;cAAA8C,QAAA,EAAC;YAEtE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvD,OAAA;cAAQ6D,IAAI,EAAC,QAAQ;cAAChB,SAAS,EAAC,iBAAiB;cAAAI,QAAA,EAAC;YAElD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnD,EAAA,CAvOIH,gBAAiD;AAAAkE,EAAA,GAAjDlE,gBAAiD;AAyOvD,eAAeA,gBAAgB;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}