[{"C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\data\\mockJobs.ts": "3", "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\JobsSection.tsx": "4", "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\Header.tsx": "5", "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\Hero.tsx": "6", "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\ApplicationModal.tsx": "7", "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\JobCard.tsx": "8", "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\SearchFilters.tsx": "9"}, {"size": 357, "mtime": 1758551190840, "results": "10", "hashOfConfig": "11"}, {"size": 1544, "mtime": 1758551869939, "results": "12", "hashOfConfig": "11"}, {"size": 2229, "mtime": 1758551115577, "results": "13", "hashOfConfig": "11"}, {"size": 2573, "mtime": 1758551464251, "results": "14", "hashOfConfig": "11"}, {"size": 1601, "mtime": 1758551207840, "results": "15", "hashOfConfig": "11"}, {"size": 1388, "mtime": 1758551430829, "results": "16", "hashOfConfig": "11"}, {"size": 7842, "mtime": 1758551922981, "results": "17", "hashOfConfig": "11"}, {"size": 2490, "mtime": 1758551447928, "results": "18", "hashOfConfig": "11"}, {"size": 3134, "mtime": 1758551481484, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lzyrr4", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\data\\mockJobs.ts", [], [], "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\JobsSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\ApplicationModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\JobCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\dev\\saas_jobseeker\\client\\src\\components\\SearchFilters.tsx", [], []]