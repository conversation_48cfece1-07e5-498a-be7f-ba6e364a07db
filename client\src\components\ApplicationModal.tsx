import React, { useState, useEffect, useRef } from 'react';
import { ApplicationModalProps, ApplicationData } from '../types';
import './ApplicationModal.css';

const ApplicationModal: React.FC<ApplicationModalProps> = ({ job, onClose }) => {
  const [formData, setFormData] = useState<ApplicationData>({
    jobId: job.id,
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    coverLetter: '',
    resumeFile: null
  });

  const modalRef = useRef<HTMLDivElement>(null);
  const firstInputRef = useRef<HTMLInputElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    // Store the previously focused element
    previousFocusRef.current = document.activeElement as HTMLElement;
    
    // Focus the first input when modal opens
    if (firstInputRef.current) {
      firstInputRef.current.focus();
    }
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
    
    // Cleanup function
    return () => {
      document.body.style.overflow = 'unset';
      // Return focus to the previously focused element
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>): void => {
    const target = e.target as HTMLInputElement;
    const { name, value, files } = target;
    setFormData(prev => ({
      ...prev,
      [name]: files ? files[0] : value
    }));
  };

  const handleSubmit = (e: React.FormEvent): void => {
    e.preventDefault();
    // Simulate form submission
    alert('Application submitted successfully! (This is a demo)');
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent): void => {
    if (e.key === 'Escape') {
      onClose();
    }
    
    // Trap focus within modal
    if (e.key === 'Tab' && modalRef.current) {
      const focusableElements = modalRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement?.focus();
      } else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement?.focus();
      }
    }
  };

  const handleBackdropClick = (e: React.MouseEvent): void => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="modal-backdrop" 
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <div className="modal-content" ref={modalRef}>
        <div className="modal-header">
          <h2 id="modal-title" className="modal-title">
            Apply for {job.title}
          </h2>
          <button
            className="modal-close"
            onClick={onClose}
            aria-label="Close application form"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        
        <div className="modal-body">
          <div id="modal-description" className="job-summary">
            <p><strong>Agency:</strong> {job.agency}</p>
            <p><strong>Location:</strong> {job.location}</p>
            <p><strong>Salary:</strong> {job.salary}</p>
          </div>
          
          <form onSubmit={handleSubmit} className="application-form">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="firstName" className="form-label">
                  First Name <span className="required" aria-label="required">*</span>
                </label>
                <input
                  ref={firstInputRef}
                  id="firstName"
                  name="firstName"
                  type="text"
                  className="form-input"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  required
                  aria-required="true"
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="lastName" className="form-label">
                  Last Name <span className="required" aria-label="required">*</span>
                </label>
                <input
                  id="lastName"
                  name="lastName"
                  type="text"
                  className="form-input"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  required
                  aria-required="true"
                />
              </div>
            </div>
            
            <div className="form-group">
              <label htmlFor="email" className="form-label">
                Email Address <span className="required" aria-label="required">*</span>
              </label>
              <input
                id="email"
                name="email"
                type="email"
                className="form-input"
                value={formData.email}
                onChange={handleInputChange}
                required
                aria-required="true"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="phone" className="form-label">
                Phone Number
              </label>
              <input
                id="phone"
                name="phone"
                type="tel"
                className="form-input"
                value={formData.phone}
                onChange={handleInputChange}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="resume" className="form-label">
                Resume <span className="required" aria-label="required">*</span>
              </label>
              <input
                id="resume"
                name="resume"
                type="file"
                className="form-input"
                onChange={handleInputChange}
                accept=".pdf,.doc,.docx"
                required
                aria-required="true"
                aria-describedby="resume-help"
              />
              <div id="resume-help" className="form-help">
                Accepted formats: PDF, DOC, DOCX (Max 5MB)
              </div>
            </div>
            
            <div className="form-group">
              <label htmlFor="coverLetter" className="form-label">
                Cover Letter
              </label>
              <textarea
                id="coverLetter"
                name="coverLetter"
                className="form-input"
                rows={6}
                value={formData.coverLetter}
                onChange={handleInputChange}
                placeholder="Tell us why you're interested in this position..."
                aria-describedby="cover-letter-help"
              />
              <div id="cover-letter-help" className="form-help">
                Optional: Explain your interest and qualifications for this role
              </div>
            </div>
            
            <div className="modal-actions">
              <button type="button" className="btn btn-secondary" onClick={onClose}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                Submit Application
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ApplicationModal;