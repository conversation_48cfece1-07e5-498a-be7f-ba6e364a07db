"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const jobs_1 = require("../data/jobs");
const router = express_1.default.Router();
const applications = [];
let applicationIdCounter = 1;
router.post('/', (req, res) => {
    try {
        const { jobId, firstName, lastName, email, phone, coverLetter, resumeFileName } = req.body;
        const errors = [];
        if (!jobId || isNaN(parseInt(jobId))) {
            errors.push('Valid job ID is required');
        }
        if (!firstName || firstName.trim().length < 2) {
            errors.push('First name is required (minimum 2 characters)');
        }
        if (!lastName || lastName.trim().length < 2) {
            errors.push('Last name is required (minimum 2 characters)');
        }
        if (!email || !isValidEmail(email)) {
            errors.push('Valid email address is required');
        }
        if (phone && !isValidPhone(phone)) {
            errors.push('Phone number format is invalid');
        }
        if (!resumeFileName) {
            errors.push('Resume file is required');
        }
        if (errors.length > 0) {
            return res.status(400).json({
                error: 'Validation Error',
                message: 'Please correct the following errors',
                errors: errors
            });
        }
        const job = jobs_1.jobs.find(j => j.id === parseInt(jobId));
        if (!job) {
            return res.status(404).json({
                error: 'Not Found',
                message: 'Job not found'
            });
        }
        const closingDate = new Date(job.closingDate);
        const today = new Date();
        if (closingDate < today) {
            return res.status(400).json({
                error: 'Application Closed',
                message: 'The application period for this job has ended'
            });
        }
        const application = {
            id: applicationIdCounter++,
            jobId: parseInt(jobId),
            jobTitle: job.title,
            agency: job.agency,
            applicant: {
                firstName: firstName.trim(),
                lastName: lastName.trim(),
                email: email.toLowerCase().trim(),
                phone: phone ? phone.trim() : null
            },
            coverLetter: coverLetter ? coverLetter.trim() : null,
            resumeFileName: resumeFileName.trim(),
            submittedAt: new Date().toISOString(),
            status: 'submitted',
            applicationNumber: `WA-${Date.now()}-${applicationIdCounter}`
        };
        applications.push(application);
        console.log('📝 New Application Received:');
        console.log(`   Application #: ${application.applicationNumber}`);
        console.log(`   Job: ${job.title} (${job.agency})`);
        console.log(`   Applicant: ${firstName} ${lastName} (${email})`);
        console.log(`   Submitted: ${application.submittedAt}`);
        console.log('---');
        const response = {
            success: true,
            message: 'Application submitted successfully',
            application: {
                applicationNumber: application.applicationNumber,
                jobTitle: job.title,
                agency: job.agency,
                submittedAt: application.submittedAt,
                status: application.status,
                nextSteps: [
                    'Your application has been received and will be reviewed by the hiring team',
                    'You will receive an email confirmation shortly',
                    'Shortlisted candidates will be contacted within 2 weeks',
                    'Please check your email regularly for updates'
                ],
                contactEmail: job.contactEmail
            }
        };
        res.status(201).json(response);
    }
    catch (error) {
        console.error('Error processing application:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: 'Failed to process application. Please try again later.'
        });
    }
});
router.get('/status/:applicationNumber', (req, res) => {
    try {
        const { applicationNumber } = req.params;
        const application = applications.find(app => app.applicationNumber === applicationNumber);
        if (!application) {
            return res.status(404).json({
                error: 'Not Found',
                message: 'Application not found'
            });
        }
        res.json({
            applicationNumber: application.applicationNumber,
            jobTitle: application.jobTitle,
            agency: application.agency,
            status: application.status,
            submittedAt: application.submittedAt,
            lastUpdated: application.submittedAt
        });
    }
    catch (error) {
        console.error('Error checking application status:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: 'Failed to check application status'
        });
    }
});
router.get('/stats', (req, res) => {
    try {
        const stats = {
            totalApplications: applications.length,
            applicationsByJob: {},
            applicationsByAgency: {},
            recentApplications: applications
                .sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())
                .slice(0, 10)
                .map(app => ({
                applicationNumber: app.applicationNumber,
                jobTitle: app.jobTitle,
                agency: app.agency,
                submittedAt: app.submittedAt
            }))
        };
        applications.forEach(app => {
            stats.applicationsByJob[app.jobTitle] =
                (stats.applicationsByJob[app.jobTitle] || 0) + 1;
        });
        applications.forEach(app => {
            stats.applicationsByAgency[app.agency] =
                (stats.applicationsByAgency[app.agency] || 0) + 1;
        });
        res.json(stats);
    }
    catch (error) {
        console.error('Error fetching application stats:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: 'Failed to fetch application statistics'
        });
    }
});
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidPhone(phone) {
    const phoneRegex = /^(\+61|0)[2-9]\d{8}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    return phoneRegex.test(cleanPhone);
}
exports.default = router;
//# sourceMappingURL=applications.js.map