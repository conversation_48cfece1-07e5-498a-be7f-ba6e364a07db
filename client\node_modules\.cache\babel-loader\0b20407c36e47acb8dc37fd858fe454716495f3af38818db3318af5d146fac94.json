{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\dev\\\\saas_jobseeker\\\\client\\\\src\\\\components\\\\JobCard.tsx\";\nimport React from 'react';\nimport './JobCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst JobCard = ({\n  job,\n  onApply\n}) => {\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-AU', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric'\n    });\n  };\n  const handleApplyClick = () => {\n    onApply(job);\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' || e.key === ' ') {\n      e.preventDefault();\n      handleApplyClick();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"article\", {\n    className: \"job-card\",\n    role: \"listitem\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"job-title\",\n        children: job.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"job-type\",\n        \"aria-label\": `Job type: ${job.type}`,\n        children: job.type\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-details\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-meta\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"meta-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"meta-label\",\n            children: \"Agency:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"meta-value\",\n            children: job.agency\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"meta-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"meta-label\",\n            children: \"Location:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"meta-value\",\n            children: job.location\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"meta-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"meta-label\",\n            children: \"Salary:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"meta-value\",\n            children: job.salary\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"meta-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"meta-label\",\n            children: \"Closing Date:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"time\", {\n            className: \"meta-value closing-date\",\n            dateTime: job.closingDate,\n            \"aria-label\": `Applications close on ${formatDate(job.closingDate)}`,\n            children: formatDate(job.closingDate)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"job-description\",\n        children: job.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary apply-btn\",\n        onClick: handleApplyClick,\n        onKeyPress: handleKeyPress,\n        \"aria-label\": `Apply for ${job.title} position`,\n        children: \"Apply Now\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        \"aria-label\": `View more details for ${job.title} position`,\n        children: \"View Details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_c = JobCard;\nexport default JobCard;\nvar _c;\n$RefreshReg$(_c, \"JobCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "JobCard", "job", "onApply", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "day", "month", "year", "handleApplyClick", "handleKeyPress", "e", "key", "preventDefault", "className", "role", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "agency", "location", "salary", "dateTime", "closingDate", "description", "onClick", "onKeyPress", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/dev/saas_jobseeker/client/src/components/JobCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { JobCardProps } from '../types';\nimport './JobCard.css';\n\nconst JobCard: React.FC<JobCardProps> = ({ job, onApply }) => {\n  const formatDate = (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-AU', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric'\n    });\n  };\n\n  const handleApplyClick = (): void => {\n    onApply(job);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent): void => {\n    if (e.key === 'Enter' || e.key === ' ') {\n      e.preventDefault();\n      handleApplyClick();\n    }\n  };\n\n  return (\n    <article className=\"job-card\" role=\"listitem\">\n      <div className=\"job-header\">\n        <h3 className=\"job-title\">{job.title}</h3>\n        <span className=\"job-type\" aria-label={`Job type: ${job.type}`}>\n          {job.type}\n        </span>\n      </div>\n      \n      <div className=\"job-details\">\n        <div className=\"job-meta\">\n          <div className=\"meta-item\">\n            <span className=\"meta-label\">Agency:</span>\n            <span className=\"meta-value\">{job.agency}</span>\n          </div>\n          <div className=\"meta-item\">\n            <span className=\"meta-label\">Location:</span>\n            <span className=\"meta-value\">{job.location}</span>\n          </div>\n          <div className=\"meta-item\">\n            <span className=\"meta-label\">Salary:</span>\n            <span className=\"meta-value\">{job.salary}</span>\n          </div>\n          <div className=\"meta-item\">\n            <span className=\"meta-label\">Closing Date:</span>\n            <time \n              className=\"meta-value closing-date\" \n              dateTime={job.closingDate}\n              aria-label={`Applications close on ${formatDate(job.closingDate)}`}\n            >\n              {formatDate(job.closingDate)}\n            </time>\n          </div>\n        </div>\n        \n        <p className=\"job-description\">{job.description}</p>\n      </div>\n      \n      <div className=\"job-actions\">\n        <button\n          className=\"btn btn-primary apply-btn\"\n          onClick={handleApplyClick}\n          onKeyPress={handleKeyPress}\n          aria-label={`Apply for ${job.title} position`}\n        >\n          Apply Now\n        </button>\n        <button\n          className=\"btn btn-secondary\"\n          aria-label={`View more details for ${job.title} position`}\n        >\n          View Details\n        </button>\n      </div>\n    </article>\n  );\n};\n\nexport default JobCard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAA+B,GAAGA,CAAC;EAAEC,GAAG;EAAEC;AAAQ,CAAC,KAAK;EAC5D,MAAMC,UAAU,GAAIC,UAAkB,IAAa;IACjD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAY;IACnCT,OAAO,CAACD,GAAG,CAAC;EACd,CAAC;EAED,MAAMW,cAAc,GAAIC,CAAsB,IAAW;IACvD,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;MACtCD,CAAC,CAACE,cAAc,CAAC,CAAC;MAClBJ,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC;EAED,oBACEZ,OAAA;IAASiB,SAAS,EAAC,UAAU;IAACC,IAAI,EAAC,UAAU;IAAAC,QAAA,gBAC3CnB,OAAA;MAAKiB,SAAS,EAAC,YAAY;MAAAE,QAAA,gBACzBnB,OAAA;QAAIiB,SAAS,EAAC,WAAW;QAAAE,QAAA,EAAEjB,GAAG,CAACkB;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1CxB,OAAA;QAAMiB,SAAS,EAAC,UAAU;QAAC,cAAY,aAAaf,GAAG,CAACuB,IAAI,EAAG;QAAAN,QAAA,EAC5DjB,GAAG,CAACuB;MAAI;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENxB,OAAA;MAAKiB,SAAS,EAAC,aAAa;MAAAE,QAAA,gBAC1BnB,OAAA;QAAKiB,SAAS,EAAC,UAAU;QAAAE,QAAA,gBACvBnB,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAAAE,QAAA,gBACxBnB,OAAA;YAAMiB,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3CxB,OAAA;YAAMiB,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAEjB,GAAG,CAACwB;UAAM;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNxB,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAAAE,QAAA,gBACxBnB,OAAA;YAAMiB,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7CxB,OAAA;YAAMiB,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAEjB,GAAG,CAACyB;UAAQ;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACNxB,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAAAE,QAAA,gBACxBnB,OAAA;YAAMiB,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3CxB,OAAA;YAAMiB,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAEjB,GAAG,CAAC0B;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNxB,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAAAE,QAAA,gBACxBnB,OAAA;YAAMiB,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjDxB,OAAA;YACEiB,SAAS,EAAC,yBAAyB;YACnCY,QAAQ,EAAE3B,GAAG,CAAC4B,WAAY;YAC1B,cAAY,yBAAyB1B,UAAU,CAACF,GAAG,CAAC4B,WAAW,CAAC,EAAG;YAAAX,QAAA,EAElEf,UAAU,CAACF,GAAG,CAAC4B,WAAW;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxB,OAAA;QAAGiB,SAAS,EAAC,iBAAiB;QAAAE,QAAA,EAAEjB,GAAG,CAAC6B;MAAW;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAENxB,OAAA;MAAKiB,SAAS,EAAC,aAAa;MAAAE,QAAA,gBAC1BnB,OAAA;QACEiB,SAAS,EAAC,2BAA2B;QACrCe,OAAO,EAAEpB,gBAAiB;QAC1BqB,UAAU,EAAEpB,cAAe;QAC3B,cAAY,aAAaX,GAAG,CAACkB,KAAK,WAAY;QAAAD,QAAA,EAC/C;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxB,OAAA;QACEiB,SAAS,EAAC,mBAAmB;QAC7B,cAAY,yBAAyBf,GAAG,CAACkB,KAAK,WAAY;QAAAD,QAAA,EAC3D;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACU,EAAA,GA7EIjC,OAA+B;AA+ErC,eAAeA,OAAO;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}