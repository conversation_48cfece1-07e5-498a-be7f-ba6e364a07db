{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\dev\\\\saas_jobseeker\\\\client\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport JobsSection from './components/JobsSection';\nimport ApplicationModal from './components/ApplicationModal';\nimport { mockJobs } from './data/mockJobs';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isHighContrast, setIsHighContrast] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [filteredJobs, setFilteredJobs] = useState(mockJobs);\n  const toggleHighContrast = () => {\n    setIsHighContrast(!isHighContrast);\n    document.documentElement.setAttribute('data-theme', !isHighContrast ? 'high-contrast' : 'default');\n  };\n  const handleApply = job => {\n    setSelectedJob(job);\n  };\n  const closeModal = () => {\n    setSelectedJob(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"a\", {\n      href: \"#main-content\",\n      className: \"skip-link\",\n      children: \"Skip to main content\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Header, {\n      isHighContrast: isHighContrast,\n      onToggleContrast: toggleHighContrast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      id: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(JobsSection, {\n        jobs: filteredJobs,\n        onJobsFilter: setFilteredJobs,\n        onApply: handleApply\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), selectedJob && /*#__PURE__*/_jsxDEV(ApplicationModal, {\n      job: selectedJob,\n      onClose: closeModal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"ChbbUj6DOdUvkkP2IU5t8Uw9NHw=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "Header", "Hero", "JobsSection", "ApplicationModal", "mockJobs", "jsxDEV", "_jsxDEV", "App", "_s", "isHighContrast", "setIsHighContrast", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "filteredJobs", "setFilteredJobs", "toggleHighContrast", "document", "documentElement", "setAttribute", "handleApply", "job", "closeModal", "className", "children", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onToggleContrast", "id", "jobs", "onJobs<PERSON><PERSON><PERSON>", "onApply", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/dev/saas_jobseeker/client/src/App.tsx"], "sourcesContent": ["import { useState } from 'react';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport JobsSection from './components/JobsSection';\nimport ApplicationModal from './components/ApplicationModal';\nimport { mockJobs } from './data/mockJobs';\nimport { Job } from './types';\nimport './App.css';\n\nfunction App(): JSX.Element {\n  const [isHighContrast, setIsHighContrast] = useState<boolean>(false);\n  const [selectedJob, setSelectedJob] = useState<Job | null>(null);\n  const [filteredJobs, setFilteredJobs] = useState<Job[]>(mockJobs);\n\n  const toggleHighContrast = (): void => {\n    setIsHighContrast(!isHighContrast);\n    document.documentElement.setAttribute(\n      'data-theme',\n      !isHighContrast ? 'high-contrast' : 'default'\n    );\n  };\n\n  const handleApply = (job: Job): void => {\n    setSelectedJob(job);\n  };\n\n  const closeModal = (): void => {\n    setSelectedJob(null);\n  };\n\n  return (\n    <div className=\"App\">\n      <a href=\"#main-content\" className=\"skip-link\">\n        Skip to main content\n      </a>\n      \n      <Header \n        isHighContrast={isHighContrast}\n        onToggleContrast={toggleHighContrast}\n      />\n      \n      <main id=\"main-content\">\n        <Hero />\n        <JobsSection \n          jobs={filteredJobs}\n          onJobsFilter={setFilteredJobs}\n          onApply={handleApply}\n        />\n      </main>\n      \n      {selectedJob && (\n        <ApplicationModal \n          job={selectedJob}\n          onClose={closeModal}\n        />\n      )}\n    </div>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SAASC,QAAQ,QAAQ,iBAAiB;AAE1C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAgB;EAAAC,EAAA;EAC1B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGX,QAAQ,CAAU,KAAK,CAAC;EACpE,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAa,IAAI,CAAC;EAChE,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAQK,QAAQ,CAAC;EAEjE,MAAMW,kBAAkB,GAAGA,CAAA,KAAY;IACrCL,iBAAiB,CAAC,CAACD,cAAc,CAAC;IAClCO,QAAQ,CAACC,eAAe,CAACC,YAAY,CACnC,YAAY,EACZ,CAACT,cAAc,GAAG,eAAe,GAAG,SACtC,CAAC;EACH,CAAC;EAED,MAAMU,WAAW,GAAIC,GAAQ,IAAW;IACtCR,cAAc,CAACQ,GAAG,CAAC;EACrB,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAY;IAC7BT,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,oBACEN,OAAA;IAAKgB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBjB,OAAA;MAAGkB,IAAI,EAAC,eAAe;MAACF,SAAS,EAAC,WAAW;MAAAC,QAAA,EAAC;IAE9C;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEJtB,OAAA,CAACN,MAAM;MACLS,cAAc,EAAEA,cAAe;MAC/BoB,gBAAgB,EAAEd;IAAmB;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAEFtB,OAAA;MAAMwB,EAAE,EAAC,cAAc;MAAAP,QAAA,gBACrBjB,OAAA,CAACL,IAAI;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACRtB,OAAA,CAACJ,WAAW;QACV6B,IAAI,EAAElB,YAAa;QACnBmB,YAAY,EAAElB,eAAgB;QAC9BmB,OAAO,EAAEd;MAAY;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAENjB,WAAW,iBACVL,OAAA,CAACH,gBAAgB;MACfiB,GAAG,EAAET,WAAY;MACjBuB,OAAO,EAAEb;IAAW;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACpB,EAAA,CAjDQD,GAAG;AAAA4B,EAAA,GAAH5B,GAAG;AAmDZ,eAAeA,GAAG;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}