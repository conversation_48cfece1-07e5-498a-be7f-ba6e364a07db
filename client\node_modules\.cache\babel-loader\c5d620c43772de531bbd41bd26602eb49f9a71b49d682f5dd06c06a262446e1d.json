{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\dev\\\\saas_jobseeker\\\\client\\\\src\\\\components\\\\Header.tsx\";\nimport React from 'react';\nimport './Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  isHighContrast,\n  onToggleContrast\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    role: \"banner\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/api/placeholder/60/60\",\n            alt: \"Western Australia Government Logo\",\n            className: \"logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"site-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"WA Government\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"subtitle\",\n              children: \"Jobs Board\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"header-nav\",\n          role: \"navigation\",\n          \"aria-label\": \"Main navigation\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"nav-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#jobs\",\n                className: \"nav-link\",\n                children: \"Browse Jobs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#about\",\n                className: \"nav-link\",\n                children: \"About\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#contact\",\n                className: \"nav-link\",\n                children: \"Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"accessibility-controls\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onToggleContrast,\n            className: \"contrast-toggle\",\n            \"aria-label\": `${isHighContrast ? 'Disable' : 'Enable'} high contrast mode`,\n            \"aria-pressed\": isHighContrast,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"contrast-icon\",\n              \"aria-hidden\": \"true\",\n              children: \"\\u25D0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), \"High Contrast\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Header", "isHighContrast", "onToggleContrast", "className", "role", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/dev/saas_jobseeker/client/src/components/Header.tsx"], "sourcesContent": ["import React from 'react';\nimport { HeaderProps } from '../types';\nimport './Header.css';\n\nconst Header: React.FC<HeaderProps> = ({ isHighContrast, onToggleContrast }) => {\n  return (\n    <header className=\"header\" role=\"banner\">\n      <div className=\"container\">\n        <div className=\"header-content\">\n          <div className=\"logo-section\">\n            <img \n              src=\"/api/placeholder/60/60\" \n              alt=\"Western Australia Government Logo\"\n              className=\"logo\"\n            />\n            <div className=\"site-title\">\n              <h1>WA Government</h1>\n              <span className=\"subtitle\">Jobs Board</span>\n            </div>\n          </div>\n          \n          <nav className=\"header-nav\" role=\"navigation\" aria-label=\"Main navigation\">\n            <ul className=\"nav-list\">\n              <li><a href=\"#jobs\" className=\"nav-link\">Browse Jobs</a></li>\n              <li><a href=\"#about\" className=\"nav-link\">About</a></li>\n              <li><a href=\"#contact\" className=\"nav-link\">Contact</a></li>\n            </ul>\n          </nav>\n          \n          <div className=\"accessibility-controls\">\n            <button\n              onClick={onToggleContrast}\n              className=\"contrast-toggle\"\n              aria-label={`${isHighContrast ? 'Disable' : 'Enable'} high contrast mode`}\n              aria-pressed={isHighContrast}\n            >\n              <span className=\"contrast-icon\" aria-hidden=\"true\">◐</span>\n              High Contrast\n            </button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAA6B,GAAGA,CAAC;EAAEC,cAAc;EAAEC;AAAiB,CAAC,KAAK;EAC9E,oBACEH,OAAA;IAAQI,SAAS,EAAC,QAAQ;IAACC,IAAI,EAAC,QAAQ;IAAAC,QAAA,eACtCN,OAAA;MAAKI,SAAS,EAAC,WAAW;MAAAE,QAAA,eACxBN,OAAA;QAAKI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BN,OAAA;UAAKI,SAAS,EAAC,cAAc;UAAAE,QAAA,gBAC3BN,OAAA;YACEO,GAAG,EAAC,wBAAwB;YAC5BC,GAAG,EAAC,mCAAmC;YACvCJ,SAAS,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFZ,OAAA;YAAKI,SAAS,EAAC,YAAY;YAAAE,QAAA,gBACzBN,OAAA;cAAAM,QAAA,EAAI;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBZ,OAAA;cAAMI,SAAS,EAAC,UAAU;cAAAE,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENZ,OAAA;UAAKI,SAAS,EAAC,YAAY;UAACC,IAAI,EAAC,YAAY;UAAC,cAAW,iBAAiB;UAAAC,QAAA,eACxEN,OAAA;YAAII,SAAS,EAAC,UAAU;YAAAE,QAAA,gBACtBN,OAAA;cAAAM,QAAA,eAAIN,OAAA;gBAAGa,IAAI,EAAC,OAAO;gBAACT,SAAS,EAAC,UAAU;gBAAAE,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DZ,OAAA;cAAAM,QAAA,eAAIN,OAAA;gBAAGa,IAAI,EAAC,QAAQ;gBAACT,SAAS,EAAC,UAAU;gBAAAE,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDZ,OAAA;cAAAM,QAAA,eAAIN,OAAA;gBAAGa,IAAI,EAAC,UAAU;gBAACT,SAAS,EAAC,UAAU;gBAAAE,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENZ,OAAA;UAAKI,SAAS,EAAC,wBAAwB;UAAAE,QAAA,eACrCN,OAAA;YACEc,OAAO,EAAEX,gBAAiB;YAC1BC,SAAS,EAAC,iBAAiB;YAC3B,cAAY,GAAGF,cAAc,GAAG,SAAS,GAAG,QAAQ,qBAAsB;YAC1E,gBAAcA,cAAe;YAAAI,QAAA,gBAE7BN,OAAA;cAAMI,SAAS,EAAC,eAAe;cAAC,eAAY,MAAM;cAAAE,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAE7D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACG,EAAA,GAxCId,MAA6B;AA0CnC,eAAeA,MAAM;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}