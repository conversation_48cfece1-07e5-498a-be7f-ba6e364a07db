{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\dev\\\\saas_jobseeker\\\\client\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst rootElement = document.getElementById('root');\nif (!rootElement) throw new Error('Failed to find the root element');\nconst root = ReactDOM.createRoot(rootElement);\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 11,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsxDEV", "_jsxDEV", "rootElement", "document", "getElementById", "Error", "root", "createRoot", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Documents/dev/saas_jobseeker/client/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\n\nconst rootElement = document.getElementById('root');\nif (!rootElement) throw new Error('Failed to find the root element');\n\nconst root = ReactDOM.createRoot(rootElement);\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,WAAW,GAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC;AACnD,IAAI,CAACF,WAAW,EAAE,MAAM,IAAIG,KAAK,CAAC,iCAAiC,CAAC;AAEpE,MAAMC,IAAI,GAAGR,QAAQ,CAACS,UAAU,CAACL,WAAW,CAAC;AAC7CI,IAAI,CAACE,MAAM,cACTP,OAAA,CAACJ,KAAK,CAACY,UAAU;EAAAC,QAAA,eACfT,OAAA,CAACF,GAAG;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}