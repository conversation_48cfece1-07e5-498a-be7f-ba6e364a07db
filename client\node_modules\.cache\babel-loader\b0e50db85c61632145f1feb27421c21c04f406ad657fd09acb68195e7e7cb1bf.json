{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\dev\\\\saas_jobseeker\\\\client\\\\src\\\\components\\\\JobsSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport JobCard from './JobCard';\nimport SearchFilters from './SearchFilters';\nimport { mockJobs } from '../data/mockJobs';\nimport './JobsSection.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst JobsSection = ({\n  jobs,\n  onJobsFilter,\n  onApply\n}) => {\n  _s();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedAgency, setSelectedAgency] = useState('All Agencies');\n  const [selectedLocation, setSelectedLocation] = useState('All Locations');\n  useEffect(() => {\n    const filtered = mockJobs.filter(job => {\n      const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) || job.agency.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesAgency = selectedAgency === 'All Agencies' || job.agency === selectedAgency;\n      const matchesLocation = selectedLocation === 'All Locations' || job.location === selectedLocation;\n      return matchesSearch && matchesAgency && matchesLocation;\n    });\n    onJobsFilter(filtered);\n  }, [searchTerm, selectedAgency, selectedLocation, onJobsFilter]);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"jobs\",\n    className: \"jobs-section\",\n    \"aria-labelledby\": \"jobs-title\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        id: \"jobs-title\",\n        className: \"section-title\",\n        children: \"Browse Government Jobs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SearchFilters, {\n        searchTerm: searchTerm,\n        onSearchChange: setSearchTerm,\n        selectedAgency: selectedAgency,\n        onAgencyChange: setSelectedAgency,\n        selectedLocation: selectedLocation,\n        onLocationChange: setSelectedLocation\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"jobs-results\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-header\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"results-count\",\n            \"aria-live\": \"polite\",\n            children: [\"Showing \", jobs.length, \" job\", jobs.length !== 1 ? 's' : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"jobs-grid\",\n          role: \"list\",\n          \"aria-label\": \"Job listings\",\n          children: jobs.length > 0 ? jobs.map(job => /*#__PURE__*/_jsxDEV(JobCard, {\n            job: job,\n            onApply: onApply\n          }, job.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-results\",\n            role: \"status\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"No jobs found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Try adjusting your search criteria or filters.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(JobsSection, \"IjaeIU+edidVjrbUDlUTSt4U3WI=\");\n_c = JobsSection;\nexport default JobsSection;\nvar _c;\n$RefreshReg$(_c, \"JobsSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "JobCard", "SearchFilters", "mockJobs", "jsxDEV", "_jsxDEV", "JobsSection", "jobs", "onJobs<PERSON><PERSON><PERSON>", "onApply", "_s", "searchTerm", "setSearchTerm", "selectedAgency", "setSelectedAgency", "selectedLocation", "setSelectedLocation", "filtered", "filter", "job", "matchesSearch", "title", "toLowerCase", "includes", "agency", "matchesAgency", "matchesLocation", "location", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSearchChange", "onAgencyChange", "onLocationChange", "length", "role", "map", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/dev/saas_jobseeker/client/src/components/JobsSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport JobCard from './JobCard';\nimport SearchFilters from './SearchFilters';\nimport { mockJobs } from '../data/mockJobs';\nimport './JobsSection.css';\n\nconst JobsSection = ({ jobs, onJobsFilter, onApply }) => {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedAgency, setSelectedAgency] = useState('All Agencies');\n  const [selectedLocation, setSelectedLocation] = useState('All Locations');\n\n  useEffect(() => {\n    const filtered = mockJobs.filter(job => {\n      const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           job.agency.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesAgency = selectedAgency === 'All Agencies' || job.agency === selectedAgency;\n      const matchesLocation = selectedLocation === 'All Locations' || job.location === selectedLocation;\n      \n      return matchesSearch && matchesAgency && matchesLocation;\n    });\n    \n    onJobsFilter(filtered);\n  }, [searchTerm, selectedAgency, selectedLocation, onJobsFilter]);\n\n  return (\n    <section id=\"jobs\" className=\"jobs-section\" aria-labelledby=\"jobs-title\">\n      <div className=\"container\">\n        <h2 id=\"jobs-title\" className=\"section-title\">Browse Government Jobs</h2>\n        \n        <SearchFilters\n          searchTerm={searchTerm}\n          onSearchChange={setSearchTerm}\n          selectedAgency={selectedAgency}\n          onAgencyChange={setSelectedAgency}\n          selectedLocation={selectedLocation}\n          onLocationChange={setSelectedLocation}\n        />\n        \n        <div className=\"jobs-results\">\n          <div className=\"results-header\">\n            <p className=\"results-count\" aria-live=\"polite\">\n              Showing {jobs.length} job{jobs.length !== 1 ? 's' : ''}\n            </p>\n          </div>\n          \n          <div className=\"jobs-grid\" role=\"list\" aria-label=\"Job listings\">\n            {jobs.length > 0 ? (\n              jobs.map(job => (\n                <JobCard\n                  key={job.id}\n                  job={job}\n                  onApply={onApply}\n                />\n              ))\n            ) : (\n              <div className=\"no-results\" role=\"status\">\n                <h3>No jobs found</h3>\n                <p>Try adjusting your search criteria or filters.</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default JobsSection;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,IAAI;EAAEC,YAAY;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,cAAc,CAAC;EACpE,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,eAAe,CAAC;EAEzEC,SAAS,CAAC,MAAM;IACd,MAAMiB,QAAQ,GAAGd,QAAQ,CAACe,MAAM,CAACC,GAAG,IAAI;MACtC,MAAMC,aAAa,GAAGD,GAAG,CAACE,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC,IAC3DH,GAAG,CAACK,MAAM,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC;MAChF,MAAMG,aAAa,GAAGZ,cAAc,KAAK,cAAc,IAAIM,GAAG,CAACK,MAAM,KAAKX,cAAc;MACxF,MAAMa,eAAe,GAAGX,gBAAgB,KAAK,eAAe,IAAII,GAAG,CAACQ,QAAQ,KAAKZ,gBAAgB;MAEjG,OAAOK,aAAa,IAAIK,aAAa,IAAIC,eAAe;IAC1D,CAAC,CAAC;IAEFlB,YAAY,CAACS,QAAQ,CAAC;EACxB,CAAC,EAAE,CAACN,UAAU,EAAEE,cAAc,EAAEE,gBAAgB,EAAEP,YAAY,CAAC,CAAC;EAEhE,oBACEH,OAAA;IAASuB,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,cAAc;IAAC,mBAAgB,YAAY;IAAAC,QAAA,eACtEzB,OAAA;MAAKwB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBzB,OAAA;QAAIuB,EAAE,EAAC,YAAY;QAACC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEzE7B,OAAA,CAACH,aAAa;QACZS,UAAU,EAAEA,UAAW;QACvBwB,cAAc,EAAEvB,aAAc;QAC9BC,cAAc,EAAEA,cAAe;QAC/BuB,cAAc,EAAEtB,iBAAkB;QAClCC,gBAAgB,EAAEA,gBAAiB;QACnCsB,gBAAgB,EAAErB;MAAoB;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAEF7B,OAAA;QAAKwB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzB,OAAA;UAAKwB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BzB,OAAA;YAAGwB,SAAS,EAAC,eAAe;YAAC,aAAU,QAAQ;YAAAC,QAAA,GAAC,UACtC,EAACvB,IAAI,CAAC+B,MAAM,EAAC,MAAI,EAAC/B,IAAI,CAAC+B,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN7B,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAACU,IAAI,EAAC,MAAM;UAAC,cAAW,cAAc;UAAAT,QAAA,EAC7DvB,IAAI,CAAC+B,MAAM,GAAG,CAAC,GACd/B,IAAI,CAACiC,GAAG,CAACrB,GAAG,iBACVd,OAAA,CAACJ,OAAO;YAENkB,GAAG,EAAEA,GAAI;YACTV,OAAO,EAAEA;UAAQ,GAFZU,GAAG,CAACS,EAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGZ,CACF,CAAC,gBAEF7B,OAAA;YAAKwB,SAAS,EAAC,YAAY;YAACU,IAAI,EAAC,QAAQ;YAAAT,QAAA,gBACvCzB,OAAA;cAAAyB,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtB7B,OAAA;cAAAyB,QAAA,EAAG;YAA8C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACxB,EAAA,CA3DIJ,WAAW;AAAAmC,EAAA,GAAXnC,WAAW;AA6DjB,eAAeA,WAAW;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}