{"name": "@types/prop-types", "version": "15.7.15", "description": "TypeScript definitions for prop-types", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/prop-types", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "githubUsername": "ferd<PERSON><PERSON>", "url": "https://github.com/ferdaber"}, {"name": "<PERSON>", "githubUsername": "eps1lon", "url": "https://github.com/eps1lon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/prop-types"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "92a20bc6f48f988ae6f314daa592e457e4b7ccb6ef115535bf69c7061375a248", "typeScriptVersion": "5.1"}